<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chapter 10: Modeling with First-Order ODEs - ODE Tutorial</title>
    
    <!-- External Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>

    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                packages: {'[+]': ['ams', 'newcommand', 'configmacros']}
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>

    <style>
        .code-block {
            background-color: #f8f9fa;
            border-left: 4px solid #007acc;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0.5rem;
            overflow-x: auto;
        }
        
        .python-code { border-left-color: #3776ab; }
        .r-code { border-left-color: #276dc3; }
        
        .definition-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 0.75rem;
            margin: 1.5rem 0;
        }
        
        .theorem-box {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 0.75rem;
            margin: 1.5rem 0;
        }
        
        .example-box {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 0.75rem;
            margin: 1.5rem 0;
        }
        
        .application-box {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 0.75rem;
            margin: 1.5rem 0;
        }
        
        .nav-section {
            position: sticky;
            top: 0;
            background: white;
            z-index: 10;
            border-bottom: 2px solid #e5e7eb;
            padding: 1rem 0;
        }
        
        .chart-container {
            height: 400px;
            margin: 2rem 0;
            background: white;
            border-radius: 0.5rem;
            padding: 1rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .chart-container canvas {
            max-width: 100%;
            height: 100% !important;
        }
        
        .large-chart {
            height: 600px;
            margin: 2rem 0;
            background: white;
            border-radius: 0.5rem;
            padding: 1rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .large-chart canvas {
            max-width: 100%;
            height: 100% !important;
        }
        
        /* Ensure charts are visible */
        canvas {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }
        
        /* Loading animation for charts */
        .chart-container:not(.loaded) {
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0, #f0f0f0);
            background-size: 400% 400%;
            animation: gradient-loading 2s ease-in-out infinite;
        }
        
        .chart-container:not(.loaded)::before {
            content: "Loading visualization...";
            color: #666;
            font-size: 14px;
        }
        
        @keyframes gradient-loading {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        @media print {
            .nav-section { position: static; }
            .chart-container, .large-chart { 
                height: 400px; 
                page-break-inside: avoid; 
            }
        }
    </style>
</head>

<body class="bg-gray-50">
    <!-- Header -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-8">
        <div class="container mx-auto px-4">
            <h1 class="text-4xl font-bold mb-2">
                <i class="fas fa-flask mr-3"></i>Chapter 10: Modeling with First-Order ODEs
            </h1>
            <p class="text-xl opacity-90">Part 2: First-Order Differential Equations - Comprehensive Applications</p>
            <div class="mt-4 text-sm opacity-75">
                <span class="mr-4"><i class="fas fa-book mr-1"></i>Advanced Tutorial</span>
                <span class="mr-4"><i class="fas fa-code mr-1"></i>Python & R</span>
                <span><i class="fas fa-chart-line mr-1"></i>Real-World Applications</span>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <div class="nav-section">
        <div class="container mx-auto px-4">
            <nav class="flex flex-wrap gap-4 text-sm">
                <a href="#prerequisites" class="text-blue-600 hover:text-blue-800 font-medium">Prerequisites</a>
                <a href="#intro" class="text-blue-600 hover:text-blue-800 font-medium">Introduction</a>
                <a href="#methodology" class="text-blue-600 hover:text-blue-800 font-medium">Modeling Methodology</a>
                <a href="#mixing" class="text-blue-600 hover:text-blue-800 font-medium">Mixing Problems</a>
                <a href="#population" class="text-blue-600 hover:text-blue-800 font-medium">Population Dynamics</a>
                <a href="#economics" class="text-blue-600 hover:text-blue-800 font-medium">Economic Models</a>
                <a href="#physics" class="text-blue-600 hover:text-blue-800 font-medium">Physics Applications</a>
                <a href="#pharmacology" class="text-blue-600 hover:text-blue-800 font-medium">Clinical Pharmacology</a>
                <a href="#engineering" class="text-blue-600 hover:text-blue-800 font-medium">Engineering Systems</a>
                <a href="#advanced" class="text-blue-600 hover:text-blue-800 font-medium">Advanced Analysis</a>
                <a href="#case-studies" class="text-blue-600 hover:text-blue-800 font-medium">Case Studies</a>
            </nav>
        </div>
    </div>

    <div class="container mx-auto px-4 py-8">

        <!-- Prerequisites -->
        <section id="prerequisites" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-graduation-cap text-indigo-600 mr-3"></i>Prerequisites and Mathematical Foundations
            </h2>

            <div class="definition-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-book mr-2"></i>Required Mathematical Background</h3>
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-bold mb-3">Calculus Foundations</h4>
                        <ul class="list-disc list-inside space-y-2 text-sm">
                            <li><strong>Derivatives:</strong> Understanding $\frac{dy}{dx}$ as rate of change</li>
                            <li><strong>Integration:</strong> Antiderivatives and definite integrals</li>
                            <li><strong>Chain Rule:</strong> For composite functions</li>
                            <li><strong>Implicit Differentiation:</strong> For related rates</li>
                            <li><strong>Exponential/Logarithmic Functions:</strong> Properties and derivatives</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-bold mb-3">Differential Equations Basics</h4>
                        <ul class="list-disc list-inside space-y-2 text-sm">
                            <li><strong>Order and Degree:</strong> Classification of ODEs</li>
                            <li><strong>Initial Value Problems:</strong> Solutions with conditions</li>
                            <li><strong>General vs Particular Solutions:</strong> Family of curves</li>
                            <li><strong>Existence and Uniqueness:</strong> When solutions exist</li>
                            <li><strong>Direction Fields:</strong> Geometric interpretation</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="grid md:grid-cols-3 gap-6 mt-6">
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">
                        <i class="fas fa-calculator text-blue-600 mr-2"></i>Solution Methods Review
                    </h3>
                    <div class="space-y-3">
                        <div class="p-3 bg-blue-50 rounded">
                            <strong>Separable ODEs:</strong>
                            <p class="text-sm mt-1">$\frac{dy}{dx} = g(x)h(y)$</p>
                            <p class="text-xs text-gray-600">Separate variables and integrate</p>
                        </div>
                        <div class="p-3 bg-green-50 rounded">
                            <strong>Linear ODEs:</strong>
                            <p class="text-sm mt-1">$\frac{dy}{dx} + P(x)y = Q(x)$</p>
                            <p class="text-xs text-gray-600">Use integrating factor $\mu(x) = e^{\int P(x)dx}$</p>
                        </div>
                        <div class="p-3 bg-purple-50 rounded">
                            <strong>Exact ODEs:</strong>
                            <p class="text-sm mt-1">$M(x,y)dx + N(x,y)dy = 0$</p>
                            <p class="text-xs text-gray-600">When $\frac{\partial M}{\partial y} = \frac{\partial N}{\partial x}$</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">
                        <i class="fas fa-chart-line text-green-600 mr-2"></i>Key Mathematical Concepts
                    </h3>
                    <div class="space-y-3">
                        <div class="p-3 bg-yellow-50 rounded">
                            <strong>Exponential Growth/Decay:</strong>
                            <p class="text-sm mt-1">$y = Ae^{kt}$</p>
                            <p class="text-xs text-gray-600">k > 0: growth, k < 0: decay</p>
                        </div>
                        <div class="p-3 bg-orange-50 rounded">
                            <strong>Equilibrium Solutions:</strong>
                            <p class="text-sm mt-1">$\frac{dy}{dt} = 0$</p>
                            <p class="text-xs text-gray-600">Constant solutions</p>
                        </div>
                        <div class="p-3 bg-red-50 rounded">
                            <strong>Stability Analysis:</strong>
                            <p class="text-sm mt-1">Linearization near equilibria</p>
                            <p class="text-xs text-gray-600">Stable vs unstable behavior</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">
                        <i class="fas fa-laptop-code text-purple-600 mr-2"></i>Computational Tools
                    </h3>
                    <div class="space-y-3">
                        <div class="p-3 bg-indigo-50 rounded">
                            <strong>Python Libraries:</strong>
                            <p class="text-sm mt-1">scipy.integrate, numpy, matplotlib</p>
                            <p class="text-xs text-gray-600">For numerical solutions and plotting</p>
                        </div>
                        <div class="p-3 bg-cyan-50 rounded">
                            <strong>R Packages:</strong>
                            <p class="text-sm mt-1">deSolve, ggplot2, plotly</p>
                            <p class="text-xs text-gray-600">Statistical analysis and visualization</p>
                        </div>
                        <div class="p-3 bg-pink-50 rounded">
                            <strong>Symbolic Math:</strong>
                            <p class="text-sm mt-1">SymPy (Python), Mathematica</p>
                            <p class="text-xs text-gray-600">Analytical solutions</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="theorem-box mt-6">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-exclamation-triangle mr-2"></i>Important Theorems</h3>
                <div class="grid md:grid-cols-2 gap-6">
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Existence and Uniqueness Theorem</h4>
                        <p class="text-sm mb-2">For the IVP: $\frac{dy}{dx} = f(x,y)$, $y(x_0) = y_0$</p>
                        <p class="text-sm">If $f$ and $\frac{\partial f}{\partial y}$ are continuous in a rectangle containing $(x_0, y_0)$, then there exists a unique solution in some interval around $x_0$.</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Fundamental Theorem of Calculus</h4>
                        <p class="text-sm mb-2">$\frac{d}{dx}\int_{a}^{x} f(t)dt = f(x)$</p>
                        <p class="text-sm">Essential for solving ODEs by integration and understanding accumulation functions.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Introduction -->
        <section id="intro" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-rocket text-blue-600 mr-3"></i>Introduction to ODE Modeling
            </h2>
            
            <div class="definition-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-lightbulb mr-2"></i>Mathematical Modeling with ODEs</h3>
                <p class="mb-3">Mathematical modeling with first-order differential equations involves:</p>
                <ul class="list-disc list-inside space-y-2">
                    <li><strong>Problem Identification:</strong> Recognizing systems that change continuously over time</li>
                    <li><strong>Mathematical Translation:</strong> Converting real-world relationships into ODE form</li>
                    <li><strong>Solution Strategy:</strong> Choosing appropriate solution methods (separable, linear, exact)</li>
                    <li><strong>Parameter Estimation:</strong> Determining model parameters from data</li>
                    <li><strong>Validation:</strong> Testing model predictions against observations</li>
                    <li><strong>Interpretation:</strong> Understanding what solutions mean in context</li>
                </ul>
            </div>

            <div class="grid md:grid-cols-2 gap-6 mt-8">
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">
                        <i class="fas fa-tools text-green-600 mr-2"></i>Solution Methods Review
                    </h3>
                    <div class="space-y-3">
                        <div class="p-3 bg-blue-50 rounded">
                            <strong>Separable:</strong> $\frac{dy}{dx} = g(x)h(y)$
                            <br><span class="text-sm text-gray-600">Growth, decay, cooling problems</span>
                        </div>
                        <div class="p-3 bg-green-50 rounded">
                            <strong>Linear:</strong> $\frac{dy}{dx} + P(x)y = Q(x)$
                            <br><span class="text-sm text-gray-600">Mixing, circuits, harvesting</span>
                        </div>
                        <div class="p-3 bg-purple-50 rounded">
                            <strong>Exact:</strong> $M dx + N dy = 0$
                            <br><span class="text-sm text-gray-600">Conservative systems, thermodynamics</span>
                        </div>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">
                        <i class="fas fa-globe text-blue-600 mr-2"></i>Application Domains
                    </h3>
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <i class="fas fa-flask text-green-500 mr-2"></i>
                            <span>Chemistry & Biology</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-chart-line text-blue-500 mr-2"></i>
                            <span>Economics & Finance</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-atom text-purple-500 mr-2"></i>
                            <span>Physics & Engineering</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-users text-orange-500 mr-2"></i>
                            <span>Population Dynamics</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-thermometer-half text-red-500 mr-2"></i>
                            <span>Environmental Science</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Modeling Methodology -->
        <section id="methodology" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-cogs text-purple-600 mr-3"></i>Systematic Modeling Methodology
            </h2>

            <div class="theorem-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-list-ol mr-2"></i>The Modeling Process</h3>
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-bold mb-2">1. Problem Formulation</h4>
                        <ul class="list-disc list-inside space-y-1 text-sm">
                            <li>Identify the system and variables</li>
                            <li>Determine what changes over time</li>
                            <li>Establish the independent variable (usually time)</li>
                            <li>Define the dependent variable(s)</li>
                        </ul>
                        
                        <h4 class="font-bold mb-2 mt-4">2. Mathematical Translation</h4>
                        <ul class="list-disc list-inside space-y-1 text-sm">
                            <li>Express rate of change: $\frac{dy}{dt}$</li>
                            <li>Identify relationships between variables</li>
                            <li>Apply physical laws or principles</li>
                            <li>Include initial/boundary conditions</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-bold mb-2">3. Solution Strategy</h4>
                        <ul class="list-disc list-inside space-y-1 text-sm">
                            <li>Classify the ODE type</li>
                            <li>Choose appropriate solution method</li>
                            <li>Solve analytically or numerically</li>
                            <li>Handle parameter estimation</li>
                        </ul>
                        
                        <h4 class="font-bold mb-2 mt-4">4. Validation & Analysis</h4>
                        <ul class="list-disc list-inside space-y-1 text-sm">
                            <li>Compare with experimental data</li>
                            <li>Perform sensitivity analysis</li>
                            <li>Check limiting behavior</li>
                            <li>Interpret results in context</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-lg mt-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-decision text-blue-600 mr-2"></i>Equation Classification Decision Tree
                </h3>
                <div class="chart-container">
                    <canvas id="classificationChart"></canvas>
                </div>
            </div>
        </section>

        <!-- Mixing Problems -->
        <section id="mixing" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-tint text-blue-600 mr-3"></i>Mixing Problems (Tank Problems)
            </h2>

            <div class="application-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-flask mr-2"></i>General Mixing Problem Setup</h3>
                <p class="mb-4">A tank contains a solution with concentration that changes over time due to inflow and outflow.</p>
                
                <div class="bg-white bg-opacity-20 p-4 rounded mb-4">
                    <h4 class="font-bold mb-2">Fundamental Principle:</h4>
                    <p class="text-center text-lg">
                        $$\frac{d(\text{Amount})}{dt} = \text{Rate In} - \text{Rate Out}$$
                    </p>
                </div>

                <div class="grid md:grid-cols-2 gap-4">
                    <div>
                        <h4 class="font-bold mb-2">Key Variables:</h4>
                        <ul class="list-disc list-inside space-y-1">
                            <li>$V(t)$ = Volume in tank at time $t$</li>
                            <li>$S(t)$ = Amount of substance at time $t$</li>
                            <li>$C(t) = S(t)/V(t)$ = Concentration</li>
                            <li>$r_{in}, r_{out}$ = Flow rates in/out</li>
                            <li>$C_{in}$ = Input concentration</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-bold mb-2">Standard Model:</h4>
                        <ul class="list-disc list-inside space-y-1">
                            <li>Rate In = $r_{in} \cdot C_{in}$</li>
                            <li>Rate Out = $r_{out} \cdot C(t)$</li>
                            <li>ODE: $\frac{dS}{dt} = r_{in}C_{in} - r_{out}C(t)$</li>
                            <li>If $V$ constant: $\frac{dC}{dt} + \frac{r}{V}C = \frac{r_{in}C_{in}}{V}$</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="example-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-calculator mr-2"></i>Example 1: Salt Water Tank</h3>
                <p class="mb-3">A tank contains 1000 L of pure water. Salt water with concentration 0.5 kg/L flows in at 10 L/min. The well-mixed solution flows out at 10 L/min.</p>
                
                <div class="bg-white bg-opacity-20 p-4 rounded mb-4">
                    <h4 class="font-bold mb-2">Solution Setup:</h4>
                    <ul class="list-disc list-inside space-y-1">
                        <li>$V = 1000$ L (constant volume)</li>
                        <li>$r_{in} = r_{out} = 10$ L/min</li>
                        <li>$C_{in} = 0.5$ kg/L</li>
                        <li>$C(0) = 0$ kg/L (initially pure water)</li>
                    </ul>
                    
                    <p class="mt-3"><strong>ODE:</strong> $\frac{dC}{dt} + \frac{10}{1000}C = \frac{10 \times 0.5}{1000}$</p>
                    <p class="mt-2">Simplifies to: $\frac{dC}{dt} + 0.01C = 0.005$</p>
                </div>

                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <h4 class="font-bold mb-2">Analytical Solution:</h4>
                    <p>This is a first-order linear ODE. Using integrating factor $\mu = e^{0.01t}$:</p>
                    <p class="text-center mt-2">$$C(t) = 0.5(1 - e^{-0.01t})$$</p>
                    <p class="mt-2">Equilibrium concentration: $C_{\infty} = 0.5$ kg/L</p>
                    <p>Time to reach 95% of equilibrium: $t = -\frac{\ln(0.05)}{0.01} \approx 300$ minutes</p>
                </div>
            </div>

            <div class="grid md:grid-cols-2 gap-6 mt-6">
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-3">
                        <i class="fab fa-python text-blue-600 mr-2"></i>Python Implementation
                    </h3>
                    <div class="code-block python-code">
                        <pre><code>import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import solve_ivp

def mixing_tank(t, y, r_in, r_out, V, C_in):
    """
    Tank mixing model
    y[0] = C(t) = concentration
    """
    C = y[0]
    dCdt = (r_in * C_in - r_out * C) / V
    return [dCdt]

# Parameters
V = 1000  # Tank volume (L)
r_in = r_out = 10  # Flow rates (L/min)
C_in = 0.5  # Input concentration (kg/L)
C0 = 0  # Initial concentration

# Time span
t_span = (0, 500)
t_eval = np.linspace(0, 500, 1000)

# Solve ODE
sol = solve_ivp(mixing_tank, t_span, [C0], t_eval=t_eval, 
                args=(r_in, r_out, V, C_in))

# Analytical solution for comparison
t_analytical = t_eval
C_analytical = C_in * (1 - np.exp(-r_out * t_analytical / V))

# Plot results
plt.figure(figsize=(10, 6))
plt.plot(sol.t, sol.y[0], 'b-', linewidth=2, 
         label='Numerical Solution')
plt.plot(t_analytical, C_analytical, 'r--', linewidth=2, 
         label='Analytical Solution')
plt.axhline(y=C_in, color='g', linestyle=':', alpha=0.7, 
            label='Equilibrium')
plt.xlabel('Time (min)')
plt.ylabel('Concentration (kg/L)')
plt.title('Salt Water Tank Mixing Problem')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()

# Parameter estimation from data
def fit_mixing_model(t_data, C_data, V, r_flow):
    """Estimate C_in from experimental data"""
    from scipy.optimize import curve_fit
    
    def model(t, C_in):
        return C_in * (1 - np.exp(-r_flow * t / V))
    
    popt, pcov = curve_fit(model, t_data, C_data)
    return popt[0], np.sqrt(pcov[0,0])

# Example with noisy data
t_data = np.array([0, 50, 100, 150, 200, 300, 400])
C_true = 0.5 * (1 - np.exp(-0.01 * t_data))
C_data = C_true + np.random.normal(0, 0.01, len(t_data))

C_in_est, C_in_err = fit_mixing_model(t_data, C_data, V, r_out)
print(f"Estimated C_in: {C_in_est:.3f} ± {C_in_err:.3f} kg/L")

# Sensitivity analysis
def sensitivity_analysis():
    V_values = np.linspace(800, 1200, 50)
    C_final = []
    
    for V_test in V_values:
        t_final = 300
        C_eq = C_in * (1 - np.exp(-r_out * t_final / V_test))
        C_final.append(C_eq)
    
    plt.figure(figsize=(8, 5))
    plt.plot(V_values, C_final, 'b-', linewidth=2)
    plt.xlabel('Tank Volume (L)')
    plt.ylabel('Concentration at t=300 min (kg/L)')
    plt.title('Sensitivity to Tank Volume')
    plt.grid(True, alpha=0.3)
    plt.show()

sensitivity_analysis()</code></pre>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-3">
                        <i class="fab fa-r-project text-blue-800 mr-2"></i>R Implementation
                    </h3>
                    <div class="code-block r-code">
                        <pre><code>library(deSolve)
library(ggplot2)
library(dplyr)

# Define mixing tank model
mixing_tank <- function(t, state, parms) {
  with(as.list(c(state, parms)), {
    C <- state[1]
    dCdt <- (r_in * C_in - r_out * C) / V
    return(list(dCdt))
  })
}

# Parameters
parms <- list(
  V = 1000,      # Tank volume (L)
  r_in = 10,     # Inflow rate (L/min)
  r_out = 10,    # Outflow rate (L/min)
  C_in = 0.5     # Input concentration (kg/L)
)

# Initial conditions and time
initial_state <- c(C = 0)  # Initial concentration
times <- seq(0, 500, by = 1)

# Solve ODE
solution <- ode(y = initial_state, 
                times = times, 
                func = mixing_tank, 
                parms = parms)

# Convert to data frame
sol_df <- as.data.frame(solution)

# Analytical solution
sol_df$C_analytical <- with(parms, 
  C_in * (1 - exp(-r_out * sol_df$time / V)))

# Create visualization
p1 <- ggplot(sol_df, aes(x = time)) +
  geom_line(aes(y = C, color = "Numerical"), size = 1.2) +
  geom_line(aes(y = C_analytical, color = "Analytical"), 
            linetype = "dashed", size = 1.2) +
  geom_hline(yintercept = parms$C_in, 
             color = "green", linetype = "dotted", alpha = 0.7) +
  labs(title = "Salt Water Tank Mixing Problem",
       x = "Time (min)",
       y = "Concentration (kg/L)",
       color = "Solution") +
  theme_minimal() +
  theme(legend.position = "bottom")

print(p1)

# Parameter estimation function
estimate_parameters <- function(t_data, C_data, V, r_flow) {
  # Define model function
  model_func <- function(t, C_in) {
    C_in * (1 - exp(-r_flow * t / V))
  }
  
  # Fit model
  fit <- nls(C_data ~ model_func(t_data, C_in),
             start = list(C_in = 0.4))
  
  return(summary(fit))
}

# Generate synthetic data with noise
set.seed(123)
t_data <- c(0, 50, 100, 150, 200, 300, 400)
C_true <- with(parms, C_in * (1 - exp(-r_out * t_data / V)))
C_data <- C_true + rnorm(length(t_data), 0, 0.01)

# Estimate parameters
fit_result <- estimate_parameters(t_data, C_data, 
                                  parms$V, parms$r_out)
print(fit_result)

# Sensitivity analysis
sensitivity_analysis <- function() {
  V_range <- seq(800, 1200, length.out = 50)
  t_final <- 300
  
  C_final <- sapply(V_range, function(V_test) {
    with(parms, C_in * (1 - exp(-r_out * t_final / V_test)))
  })
  
  p2 <- ggplot(data.frame(V = V_range, C_final = C_final), aes(x = V, y = C_final)) +
    geom_line(color = "blue", size = 1.2) +
    labs(title = "Sensitivity to Tank Volume",
         x = "Tank Volume (L)",
         y = "Concentration at t=300 min (kg/L)") +
    theme_minimal()
  
  return(p2)
}

sens_plot <- sensitivity_analysis()
print(sens_plot)

# Multi-tank system
multi_tank_model <- function(t, state, parms) {
  with(as.list(c(state, parms)), {
    C1 <- state[1]  # Tank 1 concentration
    C2 <- state[2]  # Tank 2 concentration
    
    # Tank 1: Input from external source
    dC1dt <- (r_in * C_in - r12 * C1) / V1
    
    # Tank 2: Input from Tank 1, output to environment
    dC2dt <- (r12 * C1 - r_out * C2) / V2
    
    return(list(c(dC1dt, dC2dt)))
  })
}

# Multi-tank parameters
multi_parms <- list(
  V1 = 1000, V2 = 800,
  r_in = 10, r12 = 10, r_out = 10,
  C_in = 0.5
)

multi_initial <- c(C1 = 0, C2 = 0)
multi_solution <- ode(y = multi_initial,
                      times = times,
                      func = multi_tank_model,
                      parms = multi_parms)

multi_df <- as.data.frame(multi_solution)

# Plot multi-tank system
p3 <- ggplot(multi_df, aes(x = time)) +
  geom_line(aes(y = C1, color = "Tank 1"), size = 1.2) +
  geom_line(aes(y = C2, color = "Tank 2"), size = 1.2) +
  labs(title = "Two-Tank Mixing System",
       x = "Time (min)",
       y = "Concentration (kg/L)",
       color = "Tank") +
  theme_minimal() +
  theme(legend.position = "bottom")

print(p3)
                        </code></pre>
                    </div>
                </div>
            </div>

            <!-- Interactive Mixing Problem Visualization -->
            <div class="bg-white p-6 rounded-lg shadow-lg mt-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-chart-line text-blue-600 mr-2"></i>Interactive Mixing Problem Visualization
                </h3>
                <p class="text-gray-600 mb-4">
                    This chart shows the concentration change over time in a salt water mixing problem. 
                    The solution approaches equilibrium concentration exponentially.
                </p>
                <div class="chart-container">
                    <canvas id="mixingChart"></canvas>
                </div>

                <!-- Interactive Controls for Mixing Problem -->
                <div class="mt-4 bg-gray-50 p-4 rounded-lg">
                    <h4 class="font-bold mb-3">Interactive Parameter Controls</h4>
                    <div class="grid md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium mb-1">Tank Volume (L)</label>
                            <input type="range" id="volumeSlider" min="500" max="2000" value="1000"
                                   class="w-full" oninput="updateMixingChart()">
                            <span id="volumeValue" class="text-sm text-gray-600">1000 L</span>
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-1">Flow Rate (L/min)</label>
                            <input type="range" id="flowSlider" min="5" max="20" value="10"
                                   class="w-full" oninput="updateMixingChart()">
                            <span id="flowValue" class="text-sm text-gray-600">10 L/min</span>
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-1">Inflow Concentration (kg/L)</label>
                            <input type="range" id="concentrationSlider" min="0.1" max="1.0" step="0.1" value="0.5"
                                   class="w-full" oninput="updateMixingChart()">
                            <span id="concentrationValue" class="text-sm text-gray-600">0.5 kg/L</span>
                        </div>
                    </div>
                </div>

                <div class="mt-4 grid md:grid-cols-3 gap-4 text-sm">
                    <div class="bg-blue-50 p-3 rounded">
                        <strong>Current Conditions:</strong><br>
                        <span id="currentVolume">Tank volume: 1000 L</span><br>
                        <span id="currentFlow">Flow rate: 10 L/min</span><br>
                        <span id="currentConcentration">Inflow concentration: 0.5 kg/L</span>
                    </div>
                    <div class="bg-green-50 p-3 rounded">
                        <strong>Calculated Parameters:</strong><br>
                        <span id="timeConstant">Time constant: τ = 100 min</span><br>
                        <span id="equilibriumTime">Equilibrium time: ~500 min</span><br>
                        <span id="halfTime">Half-time: ~69 min</span>
                    </div>
                    <div class="bg-orange-50 p-3 rounded">
                        <strong>Mathematical Model:</strong><br>
                        dC/dt = (r/V)(C_in - C)<br>
                        Solution: C(t) = C_in(1 - e^(-t/τ))<br>
                        Where τ = V/r
                    </div>
                </div>
            </div>
        </section>

        <!-- Mixing Problem Variations -->
        <div class="theorem-box mt-6">
            <h3 class="text-xl font-bold mb-4">
                <i class="fas fa-cogs mr-2"></i>Mixing Problem Variations
            </h3>
            <p class="mb-4">Real-world mixing problems often involve more complex scenarios. Here are common variations and their mathematical formulations:</p>
            
            <div class="grid md:grid-cols-2 gap-6">
                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <h4 class="font-bold mb-3"><i class="fas fa-chart-line mr-2"></i>Variable Volume Tank</h4>
                    <p class="mb-2">When inflow ≠ outflow, volume changes with time:</p>
                    <div class="bg-white bg-opacity-20 p-3 rounded mb-2">
                        <p>$$\frac{dV}{dt} = r_{in} - r_{out}$$</p>
                        <p>$$\frac{dS}{dt} = r_{in}C_{in} - r_{out}C(t)$$</p>
                        <p>$$C(t) = \frac{S(t)}{V(t)}$$</p>
                    </div>
                    <p class="text-sm">More complex as both $S(t)$ and $V(t)$ vary</p>
                </div>
                
                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <h4 class="font-bold mb-3"><i class="fas fa-wave-square mr-2"></i>Time-Varying Input</h4>
                    <p class="mb-2">Input concentration changes with time:</p>
                    <div class="bg-white bg-opacity-20 p-3 rounded mb-2">
                        <p>$$C_{in}(t) = A + B\sin(\omega t)$$</p>
                        <p>$$\frac{dC}{dt} + \frac{r}{V}C = \frac{r \cdot C_{in}(t)}{V}$$</p>
                    </div>
                    <p class="text-sm">Periodic forcing leads to oscillatory solutions</p>
                </div>
                
                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <h4 class="font-bold mb-3"><i class="fas fa-sitemap mr-2"></i>Multi-Tank Systems</h4>
                    <p class="mb-2">Connected tanks create system of ODEs:</p>
                    <div class="bg-white bg-opacity-20 p-3 rounded mb-2">
                        <p>$$\frac{dC_1}{dt} = \frac{r_{in}C_{in} - r_{12}C_1}{V_1}$$</p>
                        <p>$$\frac{dC_2}{dt} = \frac{r_{12}C_1 - r_{out}C_2}{V_2}$$</p>
                    </div>
                    <p class="text-sm">Cascade effect with time delays</p>
                </div>
                
                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <h4 class="font-bold mb-3"><i class="fas fa-exclamation-triangle mr-2"></i>Chemical Reactions</h4>
                    <p class="mb-2">Substance undergoes reaction while mixing:</p>
                    <div class="bg-white bg-opacity-20 p-3 rounded mb-2">
                        <p>$$\frac{dC}{dt} = \frac{r_{in}C_{in} - r_{out}C}{V} - kC^n$$</p>
                    </div>
                    <p class="text-sm">$k$ = reaction rate, $n$ = reaction order</p>
                </div>
            </div>
            
            <div class="mt-6 bg-white bg-opacity-20 p-4 rounded">
                <h4 class="font-bold mb-3"><i class="fas fa-lightbulb mr-2"></i>Key Solution Strategies</h4>
                <div class="grid md:grid-cols-2 gap-4">
                    <div>
                        <h5 class="font-semibold mb-2">Analytical Methods:</h5>
                        <ul class="text-sm space-y-1">
                            <li>• Integrating factor for linear cases</li>
                            <li>• Separation of variables when applicable</li>
                            <li>• Laplace transforms for complex inputs</li>
                            <li>• Steady-state analysis for equilibrium</li>
                        </ul>
                    </div>
                    <div>
                        <h5 class="font-semibold mb-2">Numerical Approaches:</h5>
                        <ul class="text-sm space-y-1">
                            <li>• Runge-Kutta methods for systems</li>
                            <li>• Adaptive step-size for stiff problems</li>
                            <li>• Parameter estimation from data</li>
                            <li>• Sensitivity and uncertainty analysis</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="mt-4 p-4 bg-white bg-opacity-20 rounded">
                <h4 class="font-bold mb-2"><i class="fas fa-industry mr-2"></i>Real-World Applications</h4>
                <div class="grid md:grid-cols-3 gap-4 text-sm">
                    <div>
                        <strong>Water Treatment:</strong>
                        <ul class="mt-1 space-y-1">
                            <li>• Chlorination systems</li>
                            <li>• pH adjustment tanks</li>
                            <li>• Coagulation processes</li>
                        </ul>
                    </div>
                    <div>
                        <strong>Chemical Processing:</strong>
                        <ul class="mt-1 space-y-1">
                            <li>• Reactor vessels</li>
                            <li>• Distillation columns</li>
                            <li>• Batch processing</li>
                        </ul>
                    </div>
                    <div>
                        <strong>Environmental:</strong>
                        <ul class="mt-1 space-y-1">
                            <li>• Lake pollution models</li>
                            <li>• Atmospheric mixing</li>
                            <li>• Groundwater contamination</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Population Dynamics -->
        <section id="population" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-users text-green-600 mr-3"></i>Population Dynamics
            </h2>

            <div class="application-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-seedling mr-2"></i>Population Growth Models</h3>
                <p class="mb-4">Population dynamics models describe how populations change over time under various conditions.</p>
                
                <div class="grid md:grid-cols-3 gap-4">
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Exponential Growth</h4>
                        <p class="text-sm mb-2">$\frac{dP}{dt} = rP$</p>
                        <p class="text-sm">Solution: $P(t) = P_0 e^{rt}$</p>
                        <p class="text-xs mt-1">Unlimited resources</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Logistic Growth</h4>
                        <p class="text-sm mb-2">$\frac{dP}{dt} = rP(1 - \frac{P}{K})$</p>
                        <p class="text-sm">S-shaped growth curve</p>
                        <p class="text-xs mt-1">Carrying capacity $K$</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">With Harvesting</h4>
                        <p class="text-sm mb-2">$\frac{dP}{dt} = rP - H$</p>
                        <p class="text-sm">Constant harvest rate</p>
                        <p class="text-xs mt-1">Sustainability analysis</p>
                    </div>
                </div>
            </div>

            <div class="example-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-calculator mr-2"></i>Example 2: Logistic Population Model</h3>
                <p class="mb-3">A population grows according to the logistic model with intrinsic growth rate $r = 0.1$ per year and carrying capacity $K = 1000$ individuals.</p>
                
                <div class="bg-white bg-opacity-20 p-4 rounded mb-4">
                    <h4 class="font-bold mb-2">Logistic Equation:</h4>
                    <p class="text-center">$$\frac{dP}{dt} = rP\left(1 - \frac{P}{K}\right) = 0.1P\left(1 - \frac{P}{1000}\right)$$</p>
                    
                    <p class="mt-3"><strong>This is separable:</strong></p>
                    <p class="text-center">$$\frac{dP}{P(1 - P/K)} = r \, dt$$</p>
                </div>

                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <h4 class="font-bold mb-2">Solution by Partial Fractions:</h4>
                    <p>$$\frac{1}{P(1 - P/K)} = \frac{A}{P} + \frac{B}{1 - P/K}$$</p>
                    <p>After partial fractions and integration:</p>
                    <p class="text-center mt-2">$$P(t) = \frac{K}{1 + \left(\frac{K}{P_0} - 1\right)e^{-rt}}$$</p>
                    <p class="mt-2"><strong>Key Features:</strong></p>
                    <ul class="list-disc list-inside space-y-1 text-sm">
                        <li>Inflection point at $P = K/2$</li>
                        <li>Maximum growth rate at $P = K/2$: $\frac{dP}{dt}|_{max} = \frac{rK}{4}$</li>
                        <li>Approaches carrying capacity: $\lim_{t \to \infty} P(t) = K$</li>
                    </ul>
                </div>
            </div>

            <div class="grid md:grid-cols-2 gap-6 mt-6">
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-3">
                        <i class="fab fa-python text-blue-600 mr-2"></i>Population Dynamics in Python
                    </h3>
                    <div class="code-block python-code">
                        <pre><code>import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import solve_ivp
from scipy.optimize import fsolve

# Population growth models
def exponential_growth(t, y, r):
    """Exponential growth: dP/dt = rP"""
    return [r * y[0]]

def logistic_growth(t, y, r, K):
    """Logistic growth: dP/dt = rP(1 - P/K)"""
    P = y[0]
    return [r * P * (1 - P/K)]

def growth_with_harvesting(t, y, r, K, H):
    """Growth with harvesting: dP/dt = rP(1 - P/K) - H"""
    P = y[0]
    return [r * P * (1 - P/K) - H]

# Parameters
r = 0.1  # Growth rate (1/year)
K = 1000  # Carrying capacity
P0 = 50   # Initial population
t_span = (0, 50)
t_eval = np.linspace(0, 50, 500)

# Solve different models
sol_exp = solve_ivp(exponential_growth, t_span, [P0], 
                    t_eval=t_eval, args=(r,))
sol_log = solve_ivp(logistic_growth, t_span, [P0], 
                    t_eval=t_eval, args=(r, K))

# Analytical solutions
t = t_eval
P_exp_analytical = P0 * np.exp(r * t)
P_log_analytical = K / (1 + (K/P0 - 1) * np.exp(-r * t))

# Plot comparison
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

# Population curves
ax1.plot(t, P_exp_analytical, 'r-', linewidth=2, 
         label='Exponential')
ax1.plot(t, P_log_analytical, 'b-', linewidth=2, 
         label='Logistic')
ax1.axhline(y=K, color='g', linestyle='--', alpha=0.7, 
            label='Carrying Capacity')
ax1.set_xlabel('Time (years)')
ax1.set_ylabel('Population')
ax1.set_title('Population Growth Models')
ax1.legend()
ax1.grid(True, alpha=0.3)
ax1.set_ylim(0, 1200)

# Growth rates
dPdt_exp = r * P_exp_analytical
dPdt_log = r * P_log_analytical * (1 - P_log_analytical/K);

ax2.plot(P_exp_analytical, dPdt_exp, 'r-', linewidth=2, 
         label='Exponential')
ax2.plot(P_log_analytical, dPdt_log, 'b-', linewidth=2, 
         label='Logistic')
ax2.axvline(x=K/2, color='purple', linestyle=':', alpha=0.7, 
            label='Max Growth Rate')
ax2.set_xlabel('Population')
ax2.set_ylabel('Growth Rate (dP/dt)')
ax2.set_title('Phase Portrait')
ax2.legend()
ax2.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Harvesting analysis
def analyze_harvesting():
    """Analyze sustainable harvesting"""
    # Critical harvesting rate (maximum sustainable)
    H_max = r * K / 4  # At P = K/2
    
    print(f"Maximum sustainable harvest rate: {H_max:.2f}")
    
    # Different harvesting scenarios
    H_values = [0, H_max/2, H_max, H_max*1.2]
    colors = ['blue', 'green', 'orange', 'red']
    labels = ['No harvest', 'Sustainable', 'Critical', 'Overharvest']
    
    plt.figure(figsize=(12, 8))
    
    for i, H in enumerate(H_values):
        if H < H_max * 1.1:  # Avoid extinction scenarios
            sol_h = solve_ivp(growth_with_harvesting, t_span, [P0], 
                             t_eval=t_eval, args=(r, K, H))
            plt.plot(sol_h.t, sol_h.y[0], color=colors[i], 
                    linewidth=2, label=f'{labels[i]} (H={H:.1f})')
    
    plt.axhline(y=K, color='gray', linestyle='--', alpha=0.5, 
                label='Carrying Capacity')
    plt.xlabel('Time (years)')
    plt.ylabel('Population')
    plt.title('Population Dynamics with Harvesting')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.show()
    
    # Equilibrium analysis
    def equilibrium_points(H):
        """Find equilibrium points for harvesting model"""
        def f(P):
            return r * P * (1 - P/K) - H
        
        if H <= H_max:
            roots = fsolve(f, [K/4, 3*K/4])
            return roots[roots > 0]  # Positive roots only
        else:
            return []
    
    H_range = np.linspace(0, H_max*1.5, 100)
    equilibria = []
    
    for H in H_range:
        eq_points = equilibrium_points(H)
        if len(eq_points) > 0:
            equilibria.extend([(H, P) for P in eq_points])
    
    if equilibria:
        H_eq, P_eq = zip(*equilibria)
        plt.figure(figsize=(10, 6))
        plt.plot(H_eq, P_eq, 'b.', markersize=3)
        plt.axvline(x=H_max, color='red', linestyle='--', 
                   label=f'Critical H = {H_max:.2f}')
        plt.xlabel('Harvest Rate')
        plt.ylabel('Equilibrium Population')
        plt.title('Bifurcation Diagram: Harvest Rate vs Population')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.show()

analyze_harvesting()

# Parameter estimation from data
def fit_logistic_model(t_data, P_data):
    """Fit logistic model to population data"""
    from scipy.optimize import curve_fit
    
    def logistic_func(t, r, K, P0):
        return K / (1 + (K/P0 - 1) * np.exp(-r * t))
    
    # Initial guess
    K_guess = max(P_data) * 1.2
    r_guess = 0.1
    P0_guess = P_data[0]
    
    popt, pcov = curve_fit(logistic_func, t_data, P_data, 
                          p0=[r_guess, K_guess, P0_guess],
                          bounds=([0, max(P_data), 0], 
                                 [1, max(P_data)*3, max(P_data)]))
    
    r_fit, K_fit, P0_fit = popt
    r_err, K_err, P0_err = np.sqrt(np.diag(pcov))
    
    return (r_fit, K_fit, P0_fit), (r_err, K_err, P0_err)

# Generate synthetic data
t_data = np.array([0, 2, 5, 10, 15, 20, 25, 30])
P_true = K / (1 + (K/P0 - 1) * np.exp(-r * t_data))
P_data = P_true + np.random.normal(0, P_true * 0.05)  # 5% noise

# Fit model
params, errors = fit_logistic_model(t_data, P_data)
r_fit, K_fit, P0_fit = params
print(f"Fitted parameters:")
print(f"r = {r_fit:.4f} ± {errors[0]:.4f}")
print(f"K = {K_fit:.1f} ± {errors[1]:.1f}")
print(f"P0 = {P0_fit:.1f} ± {errors[2]:.1f}")

# Compare with true values
P_fitted = K_fit / (1 + (K_fit/P0_fit - 1) * np.exp(-r_fit * t_eval))

plt.figure(figsize=(10, 6))
plt.plot(t_eval, P_log_analytical, 'b-', linewidth=2, label='True Model')
plt.plot(t_eval, P_fitted, 'r--', linewidth=2, label='Fitted Model')
plt.scatter(t_data, P_data, color='black', s=50, 
           label='Data Points', zorder=5)
plt.xlabel('Time (years)')
plt.ylabel('Population')
plt.title('Logistic Model Fitting')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()

# Model diagnostics - residual analysis
residuals = P_data - K_fit / (1 + (K_fit/P0_fit - 1) * np.exp(-r_fit * t_data))
fitted_values = K_fit / (1 + (K_fit/P0_fit - 1) * np.exp(-r_fit * t_data))

fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

# Residuals vs fitted values
ax1.scatter(fitted_values, residuals, color='blue', s=50)
ax1.axhline(y=0, color='red', linestyle='--', alpha=0.7)
ax1.set_xlabel('Fitted Values')
ax1.set_ylabel('Residuals')
ax1.set_title('Residual Analysis')
ax1.grid(True, alpha=0.3)

# Residuals vs time
ax2.scatter(t_data, residuals, color='blue', s=50)
ax2.plot(t_data, residuals, color='blue', alpha=0.5)
ax2.axhline(y=0, color='red', linestyle='--', alpha=0.7)
ax2.set_xlabel('Time')
ax2.set_ylabel('Residuals')
ax2.set_title('Residuals vs Time')
ax2.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()
                        </code></pre>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-3">
                        <i class="fab fa-r-project text-red-600 mr-2"></i>Population Dynamics in R
                    </h3>
                    <div class="code-block r-code">
                        <pre><code># Population growth models in R
library(deSolve)
library(ggplot2)
library(gridExtra)

# Define population growth models
exponential_growth <- function(t, y, parms) {
  with(as.list(c(y, parms)), {
    dP <- r * P
    list(c(dP))
  })
}

logistic_growth <- function(t, y, parms) {
  with(as.list(c(y, parms)), {
    dP <- r * P * (1 - P/K)
    list(c(dP))
  })
}

growth_with_harvesting <- function(t, y, parms) {
  with(as.list(c(y, parms)), {
    dP <- r * P * (1 - P/K) - H
    list(c(dP))
  })
}

# Parameters
parameters <- list(r = 0.1, K = 1000, H = 25)
initial_conditions <- c(P = 50)
times <- seq(0, 50, by = 0.1)

# Solve ODEs
sol_exp <- ode(y = initial_conditions, 
               times = times, 
               func = exponential_growth, 
               parms = list(r = parameters$r))

sol_log <- ode(y = initial_conditions, 
               times = times, 
               func = logistic_growth, 
               parms = list(r = parameters$r, K = parameters$K))

sol_harv <- ode(y = initial_conditions, 
                times = times, 
                func = growth_with_harvesting, 
                parms = parameters)

# Create data frames for plotting
df_exp <- data.frame(time = sol_exp[,1], population = sol_exp[,2], model = "Exponential")
df_log <- data.frame(time = sol_log[,1], population = sol_log[,2], model = "Logistic")
df_harv <- data.frame(time = sol_harv[,1], population = sol_harv[,2], model = "With Harvesting")

combined_df <- rbind(df_exp, df_log, df_harv)

# Analytical solutions for comparison
t_analytical <- times
P_exp_analytical <- initial_conditions[1] * exp(parameters$r * t_analytical)
P_log_analytical <- parameters$K / (1 + (parameters$K/initial_conditions[1] - 1) * 
                                   exp(-parameters$r * t_analytical))

# Plot results
p1 <- ggplot(combined_df, aes(x = time, y = population, color = model)) +
  geom_line(size = 1.2) +
  geom_hline(yintercept = parameters$K, linetype = "dashed", 
             color = "gray", alpha = 0.7) +
  labs(title = "Population Growth Models Comparison",
       x = "Time (years)",
       y = "Population",
       color = "Model") +
  theme_minimal() +
  theme(legend.position = "bottom") +
  scale_color_manual(values = c("red", "blue", "green"))

print(p1)

# Parameter estimation and model fitting
# Generate synthetic data with noise
set.seed(123)
t_data <- c(0, 2, 5, 10, 15, 20, 25, 30)
P_true <- parameters$K / (1 + (parameters$K/initial_conditions[1] - 1) * 
                         exp(-parameters$r * t_data))
P_data <- P_true + rnorm(length(t_data), 0, P_true * 0.05)  # 5% noise

# Fit logistic model using nls
logistic_model <- function(t, r, K, P0) {
  K / (1 + (K/P0 - 1) * exp(-r * t))
}

# Non-linear least squares fitting
fit_result <- nls(P_data ~ logistic_model(t_data, r, K, P0),
                  start = list(r = 0.1, K = 1200, P0 = 50),
                  algorithm = "port",
                  lower = c(0.01, max(P_data), 0),
                  upper = c(1, max(P_data)*3, max(P_data)))

# Extract fitted parameters
fitted_params <- summary(fit_result)
print("Fitted Parameters:")
print(fitted_params$parameters)

# Model predictions
t_pred <- seq(0, 40, length.out = 200)
P_pred <- predict(fit_result, newdata = list(t_data = t_pred))

# Plot fitted model
fitting_df <- data.frame(
  time = c(t_analytical, t_pred, t_data),
  population = c(P_log_analytical, P_pred, P_data),
  type = c(rep("True", length(t_analytical)), 
           rep("Fitted", length(t_pred)),
           rep("Data", length(t_data)))
)

p3 <- ggplot(fitting_df, aes(x = time, y = population)) +
  geom_line(data = subset(fitting_df, type == "True"), 
            aes(color = "True Model"), size = 1.2) +
  geom_line(data = subset(fitting_df, type == "Fitted"), 
            aes(color = "Fitted Model"), size = 1.2, linetype = "dashed") +
  geom_point(data = subset(fitting_df, type == "Data"), 
             aes(color = "Data Points"), size = 3) +
  labs(title = "Logistic Model Parameter Estimation",
       x = "Time (years)",
       y = "Population",
       color = "Legend") +
  theme_minimal() +
  scale_color_manual(values = c("blue", "red", "black"))

print(p3)
                        </code></pre>
                    </div>
                </div>
            </div>

            <div class="chart-container">
                <canvas id="populationChart"></canvas>
            </div>
        </section>

        <!-- Economic Models -->
        <section id="economics" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-chart-line text-blue-600 mr-3"></i>Economic Models
            </h2>

            <div class="application-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-dollar-sign mr-2"></i>Economic Growth and Decay</h3>
                <p class="mb-4">Economic systems often exhibit exponential or logistic behavior in investment, savings, and debt models.</p>
                
                <div class="grid md:grid-cols-3 gap-4">
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Compound Interest</h4>
                        <p class="text-sm mb-2">$\frac{dA}{dt} = rA$</p>
                        <p class="text-sm">Solution: $A(t) = A_0 e^{rt}$</p>
                        <p class="text-xs mt-1">Continuous compounding</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Savings with Deposits</h4>
                        <p class="text-sm mb-2">$\frac{dS}{dt} = rS + D$</p>
                        <p class="text-sm">Regular contributions</p>
                        <p class="text-xs mt-1">Linear first-order ODE</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Debt Repayment</h4>
                        <p class="text-sm mb-2">$\frac{dD}{dt} = rD - P$</p>
                        <p class="text-sm">Interest vs payments</p>
                        <p class="text-xs mt-1">Amortization analysis</p>
                    </div>
                </div>
            </div>

            <div class="example-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-calculator mr-2"></i>Example 3: Loan Amortization Model</h3>
                <p class="mb-3">A loan of $100,000 at 5% annual interest requires monthly payments. Model the debt balance over time.</p>
                
                <div class="bg-white bg-opacity-20 p-4 rounded mb-4">
                    <h4 class="font-bold mb-2">Debt Balance Equation:</h4>
                    <p class="text-center">$$\frac{dD}{dt} = rD - P$$</p>
                    <p class="mt-2">Where $D$ is debt balance, $r = 0.05$ is annual rate, $P$ is monthly payment rate</p>
                    
                    <p class="mt-3"><strong>This is linear first-order:</strong></p>
                    <p class="text-center">$$\frac{dD}{dt} - rD = -P$$</p>
                </div>

                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <h4 class="font-bold mb-2">Solution:</h4>
                    <p>Using integrating factor $\mu(t) = e^{-rt}$:</p>
                    <p class="text-center mt-2">$$D(t) = \left(D_0 - \frac{P}{r}\right)e^{rt} + \frac{P}{r}$$</p>
                    <p class="mt-2"><strong>Key Insights:</strong></p>
                    <ul class="list-disc list-inside space-y-1 text-sm">
                        <li>If $P > rD_0$: Debt decreases to zero</li>
                        <li>If $P = rD_0$: Interest-only payments</li>
                        <li>If $P < rD_0$: Debt grows exponentially</li>
                        <li>Time to payoff: $t_{payoff} = \frac{1}{r}\ln\left(\frac{P}{P-rD_0}\right)$</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Physics Applications -->
        <section id="physics" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-atom text-purple-600 mr-3"></i>Physics Applications
            </h2>

            <div class="application-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-thermometer-half mr-2"></i>Newton's Law of Cooling</h3>
                <p class="mb-4">Temperature changes in objects follow Newton's law of cooling, a fundamental first-order ODE.</p>
                
                <div class="grid md:grid-cols-2 gap-4">
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Newton's Law</h4>
                        <p class="text-sm mb-2">$\frac{dT}{dt} = -k(T - T_{env})$</p>
                        <p class="text-sm">Rate ∝ temperature difference</p>
                        <p class="text-xs mt-1">Separable equation</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Solution</h4>
                        <p class="text-sm mb-2">$T(t) = T_{env} + (T_0 - T_{env})e^{-kt}$</p>
                        <p class="text-sm">Exponential approach</p>
                        <p class="text-xs mt-1">To environmental temperature</p>
                    </div>
                </div>
            </div>

            <div class="example-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-calculator mr-2"></i>Example 4: Coffee Cooling Problem</h3>
                <p class="mb-3">A cup of coffee at 90°C is placed in a 20°C room. After 5 minutes, it cools to 70°C. When will it reach 30°C?</p>
                
                <div class="bg-white bg-opacity-20 p-4 rounded mb-4">
                    <h4 class="font-bold mb-2">Setup:</h4>
                    <p>$\frac{dT}{dt} = -k(T - 20)$, with $T(0) = 90$, $T(5) = 70$</p>
                    
                    <p class="mt-3"><strong>Solution:</strong></p>
                    <p>$T(t) = 20 + 70e^{-kt}$</p>
                    <p>From $T(5) = 70$: $70 = 20 + 70e^{-5k}$</p>
                    <p>Solving: $k = \frac{1}{5}\ln\left(\frac{7}{5}\right) \approx 0.0671$</p>
                </div>

                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <h4 class="font-bold mb-2">Finding when T = 30°C:</h4>
                    <p>$30 = 20 + 70e^{-0.0671t}$</p>
                    <p>$10 = 70e^{-0.0671t}$</p>
                    <p>$t = \frac{\ln(7)}{0.0671} \approx 29.0$ minutes</p>
                </div>
            </div>
        </section>

        <!-- Engineering Systems -->
        <section id="engineering" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-cogs text-orange-600 mr-3"></i>Engineering Systems
            </h2>

            <div class="application-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-bolt mr-2"></i>RC Circuits</h3>
                <p class="mb-4">Resistor-Capacitor circuits exhibit first-order behavior in charging and discharging.</p>
                
                <div class="grid md:grid-cols-2 gap-4">
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Kirchhoff's Law</h4>
                        <p class="text-sm mb-2">$RC\frac{dV_C}{dt} + V_C = V_{in}$</p>
                        <p class="text-sm">First-order linear ODE</p>
                        <p class="text-xs mt-1">Time constant τ = RC</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Charging Response</h4>
                        <p class="text-sm mb-2">$V_C(t) = V_{in}(1 - e^{-t/RC})$</p>
                        <p class="text-sm">Exponential approach</p>
                        <p class="text-xs mt-1">To input voltage</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Clinical Pharmacology -->
        <section id="pharmacology" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-pills text-green-600 mr-3"></i>Clinical Pharmacological Applications
            </h2>

            <div class="application-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-heartbeat mr-2"></i>Pharmacokinetic Modeling with First-Order ODEs</h3>
                <p class="mb-4">Pharmacokinetics describes how drugs move through the body using ADME processes: Absorption, Distribution, Metabolism, and Elimination. These processes often follow first-order kinetics.</p>

                <div class="grid md:grid-cols-3 gap-4">
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">First-Order Elimination</h4>
                        <p class="text-sm mb-2">$\frac{dC}{dt} = -k_e C$</p>
                        <p class="text-xs">Rate proportional to concentration</p>
                        <p class="text-xs mt-1">Half-life: $t_{1/2} = \frac{\ln(2)}{k_e}$</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">One-Compartment Model</h4>
                        <p class="text-sm mb-2">$\frac{dA}{dt} = -k_e A$</p>
                        <p class="text-xs">A = amount in body</p>
                        <p class="text-xs mt-1">$C(t) = C_0 e^{-k_e t}$</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Clearance Concept</h4>
                        <p class="text-sm mb-2">$CL = k_e \cdot V_d$</p>
                        <p class="text-xs">Volume cleared per unit time</p>
                        <p class="text-xs mt-1">$k_e = \frac{CL}{V_d}$</p>
                    </div>
                </div>
            </div>

            <div class="theorem-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-flask mr-2"></i>Mathematical Framework for Pharmacokinetics</h3>
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-bold mb-3">Single-Compartment Model</h4>
                        <div class="bg-white bg-opacity-20 p-4 rounded mb-3">
                            <p class="text-sm mb-2"><strong>Mass Balance Equation:</strong></p>
                            <p class="text-center">$$V_d \frac{dC}{dt} = \text{Input Rate} - CL \cdot C$$</p>
                            <p class="text-sm mt-2">Where:</p>
                            <ul class="text-xs space-y-1">
                                <li>$V_d$ = Volume of distribution</li>
                                <li>$C$ = Plasma concentration</li>
                                <li>$CL$ = Total body clearance</li>
                            </ul>
                        </div>

                        <div class="bg-white bg-opacity-20 p-4 rounded">
                            <p class="text-sm mb-2"><strong>IV Bolus Solution:</strong></p>
                            <p class="text-center">$$C(t) = \frac{D}{V_d} e^{-k_e t}$$</p>
                            <p class="text-sm mt-2">Where $k_e = \frac{CL}{V_d}$ and $D$ = dose</p>
                        </div>
                    </div>

                    <div>
                        <h4 class="font-bold mb-3">First-Order Absorption</h4>
                        <div class="bg-white bg-opacity-20 p-4 rounded mb-3">
                            <p class="text-sm mb-2"><strong>Absorption + Elimination:</strong></p>
                            <p class="text-center">$$\frac{dC}{dt} = \frac{k_a F D}{V_d} e^{-k_a t} - k_e C$$</p>
                            <p class="text-sm mt-2">Where:</p>
                            <ul class="text-xs space-y-1">
                                <li>$k_a$ = Absorption rate constant</li>
                                <li>$F$ = Bioavailability fraction</li>
                                <li>$D$ = Oral dose</li>
                            </ul>
                        </div>

                        <div class="bg-white bg-opacity-20 p-4 rounded">
                            <p class="text-sm mb-2"><strong>Analytical Solution:</strong></p>
                            <p class="text-center">$$C(t) = \frac{k_a F D}{V_d(k_a - k_e)} (e^{-k_e t} - e^{-k_a t})$$</p>
                            <p class="text-sm mt-2">Peak time: $t_{max} = \frac{\ln(k_a/k_e)}{k_a - k_e}$</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="example-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-calculator mr-2"></i>Clinical Example: Digoxin Pharmacokinetics</h3>
                <p class="mb-3">Digoxin is a cardiac glycoside with well-characterized first-order elimination kinetics. Model a 0.25 mg IV dose in a 70 kg patient.</p>

                <div class="bg-white bg-opacity-20 p-4 rounded mb-4">
                    <h4 class="font-bold mb-2">Patient Parameters:</h4>
                    <div class="grid md:grid-cols-2 gap-4">
                        <ul class="list-disc list-inside space-y-1 text-sm">
                            <li>Weight: 70 kg</li>
                            <li>Dose: 0.25 mg IV</li>
                            <li>Volume of distribution: 7 L/kg</li>
                            <li>Clearance: 1.2 mL/min/kg</li>
                        </ul>
                        <ul class="list-disc list-inside space-y-1 text-sm">
                            <li>$V_d = 7 \times 70 = 490$ L</li>
                            <li>$CL = 1.2 \times 70 = 84$ mL/min = 5.04 L/h</li>
                            <li>$k_e = \frac{5.04}{490} = 0.0103$ h⁻¹</li>
                            <li>$t_{1/2} = \frac{0.693}{0.0103} = 67.3$ hours</li>
                        </ul>
                    </div>
                </div>

                <div class="bg-white bg-opacity-20 p-4 rounded">
                    <h4 class="font-bold mb-2">Concentration-Time Profile:</h4>
                    <p class="text-center">$$C(t) = \frac{0.25 \text{ mg}}{490 \text{ L}} e^{-0.0103t} = 0.51 \times e^{-0.0103t} \text{ ng/mL}$$</p>
                    <p class="text-sm mt-2"><strong>Clinical Targets:</strong></p>
                    <ul class="text-sm space-y-1">
                        <li>Therapeutic range: 1.0-2.0 ng/mL</li>
                        <li>Toxic level: >2.5 ng/mL</li>
                        <li>Time to steady state: ~5 × t₁/₂ = 337 hours (14 days)</li>
                    </ul>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-lg mt-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-chart-line text-green-600 mr-2"></i>Pharmacokinetic Visualization Dashboard
                </h3>
                <div class="grid md:grid-cols-2 gap-6">
                    <div class="chart-container">
                        <canvas id="pharmacologyIVChart"></canvas>
                    </div>
                    <div class="chart-container">
                        <canvas id="pharmacologyOralChart"></canvas>
                    </div>
                </div>
                <div class="chart-container mt-6">
                    <canvas id="pharmacologyMultiDoseChart"></canvas>
                </div>
                <div class="mt-4 grid md:grid-cols-3 gap-4 text-sm">
                    <div class="bg-blue-50 p-3 rounded">
                        <strong>IV Bolus Model:</strong><br>
                        Immediate distribution<br>
                        First-order elimination<br>
                        Exponential decay
                    </div>
                    <div class="bg-green-50 p-3 rounded">
                        <strong>Oral Absorption:</strong><br>
                        Flip-flop kinetics possible<br>
                        Peak concentration time<br>
                        Bioavailability effects
                    </div>
                    <div class="bg-orange-50 p-3 rounded">
                        <strong>Multiple Dosing:</strong><br>
                        Accumulation to steady state<br>
                        Loading dose calculations<br>
                        Dosing interval optimization
                    </div>
                </div>
            </div>
        </section>

        <!-- Advanced Analysis -->
        <section id="advanced" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-microscope text-red-600 mr-3"></i>Advanced Analysis Techniques
            </h2>

            <div class="theorem-box">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-brain mr-2"></i>Qualitative Analysis</h3>
                <div class="grid md:grid-cols-2 gap-4">
                    <div>
                        <h4 class="font-bold mb-2">Equilibrium Points</h4>
                        <p class="text-sm mb-2">Solutions where $\frac{dy}{dt} = 0$</p>
                        <ul class="text-sm space-y-1">
                            <li>• Stable: Solutions approach equilibrium</li>
                            <li>• Unstable: Solutions move away</li>
                            <li>• Semi-stable: One-sided stability</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-bold mb-2">Phase Line Analysis</h4>
                        <p class="text-sm mb-2">Graphical representation of solution behavior</p>
                        <ul class="text-sm space-y-1">
                            <li>• Direction field arrows</li>
                            <li>• Equilibrium classification</li>
                            <li>• Long-term behavior prediction</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-lg mt-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-project-diagram text-red-600 mr-2"></i>Advanced Analysis Visualizations
                </h3>
                <div class="grid md:grid-cols-2 gap-6">
                    <div class="chart-container">
                        <canvas id="advancedPhaseChart"></canvas>
                    </div>
                    <div class="chart-container">
                        <canvas id="advancedStabilityChart"></canvas>
                    </div>
                </div>
                <div class="large-chart mt-6">
                    <canvas id="advancedDirectionChart"></canvas>
                </div>
            </div>

            <div class="example-box mt-6">
                <h3 class="text-xl font-bold mb-3"><i class="fas fa-search mr-2"></i>Bifurcation Analysis</h3>
                <p class="mb-3">Study how equilibrium behavior changes with parameter variation:</p>

                <div class="bg-white bg-opacity-20 p-4 rounded mb-4">
                    <h4 class="font-bold mb-2">Example: Harvesting Model</h4>
                    <p class="text-sm mb-2">$\frac{dP}{dt} = rP(1 - \frac{P}{K}) - H$</p>
                    <p class="text-sm">P = population, r = growth rate, K = carrying capacity, H = harvest rate</p>
                </div>

                <div class="grid md:grid-cols-2 gap-4">
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Critical Harvest Rate</h4>
                        <p class="text-sm">$H_{max} = \frac{rK}{4}$ (maximum sustainable harvest)</p>
                        <p class="text-sm">Two equilibria exist for H < H_max</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-4 rounded">
                        <h4 class="font-bold mb-2">Saddle-Node Bifurcation</h4>
                        <p class="text-sm">At H = H_max, equilibria collide and disappear</p>
                        <p class="text-sm">Population crashes for H > H_max</p>
                    </div>
                </div>
            </div>

            <div class="chart-container mt-6">
                <canvas id="advancedBifurcationChart"></canvas>
            </div>

            <div class="grid md:grid-cols-2 gap-6 mt-6">
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-3">
                        <i class="fab fa-python text-blue-600 mr-2"></i>Python: Advanced Analysis
                    </h3>
                    <div class="code-block python-code">
                        <pre><code>import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import solve_ivp
from scipy.optimize import fsolve, brentq
from matplotlib.patches import Circle
import sympy as sp

class AdvancedODEAnalysis:
    def __init__(self):
        pass

    def phase_line_analysis(self, f, y_range, title="Phase Line Analysis"):
        """Analyze phase line for autonomous ODE dy/dt = f(y)"""
        y = np.linspace(y_range[0], y_range[1], 1000)
        dydt = f(y)

        # Find equilibria (zeros of f(y))
        equilibria = []
        sign_changes = np.where(np.diff(np.sign(dydt)))[0]

        for i in sign_changes:
            try:
                eq = brentq(f, y[i], y[i+1])
                equilibria.append(eq)
            except:
                pass

        # Classify equilibria stability
        stable_eq = []
        unstable_eq = []

        for eq in equilibria:
            # Check derivative at equilibrium point
            h = 1e-6
            fprime = (f(eq + h) - f(eq - h)) / (2 * h)
            if fprime < 0:
                stable_eq.append(eq)
            else:
                unstable_eq.append(eq)

        # Plot phase line
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # Plot dy/dt vs y
        ax1.plot(y, dydt, 'b-', linewidth=2, label='dy/dt = f(y)')
        ax1.axhline(y=0, color='k', linestyle='--', alpha=0.5)
        ax1.axvline(x=0, color='k', linestyle='--', alpha=0.5)

        # Mark equilibria
        for eq in stable_eq:
            ax1.plot(eq, 0, 'go', markersize=10, label=f'Stable: y = {eq:.2f}')
        for eq in unstable_eq:
            ax1.plot(eq, 0, 'ro', markersize=10, label=f'Unstable: y = {eq:.2f}')

        ax1.set_xlabel('y')
        ax1.set_ylabel('dy/dt')
        ax1.set_title(title)
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        return equilibria, stable_eq, unstable_eq

    def bifurcation_analysis(self, f, param_range, param_name='r'):
        """Analyze bifurcations as parameter varies"""
        params = np.linspace(param_range[0], param_range[1], 200)
        equilibria = []
        stability = []

        for p in params:
            # Find equilibria for this parameter value
            eq_for_p = []
            stab_for_p = []

            # Search for equilibria in reasonable range
            y_search = np.linspace(-10, 10, 1000)
            try:
                f_vals = f(y_search, p)
                sign_changes = np.where(np.diff(np.sign(f_vals)))[0]

                for i in sign_changes:
                    try:
                        eq = brentq(lambda y: f(y, p), y_search[i], y_search[i+1])
                        eq_for_p.append(eq)

                        # Check stability
                        h = 1e-6
                        fprime = (f(eq + h, p) - f(eq - h, p)) / (2 * h)
                        stab_for_p.append(fprime < 0)  # True if stable
                    except:
                        pass
            except:
                pass

            equilibria.append(eq_for_p)
            stability.append(stab_for_p)

        return params, equilibria, stability

# Example usage
analyzer = AdvancedODEAnalysis()

# Logistic equation with harvesting
def logistic_harvesting(y, r=1, K=10, H=2):
    """dy/dt = ry(1 - y/K) - H"""
    return r * y * (1 - y/K) - H

# Phase line analysis
y_range = (-2, 12)
equilibria, stable, unstable = analyzer.phase_line_analysis(
    lambda y: logistic_harvesting(y), y_range,
    "Logistic Growth with Harvesting"
)

# Bifurcation analysis
def logistic_H_param(y, H):
    return logistic_harvesting(y, H=H)

H_range = (0, 5)
params, eqs, stabs = analyzer.bifurcation_analysis(
    logistic_H_param, H_range, 'H (Harvest Rate)'
)</code></pre>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-bold text-gray-800 mb-3">
                        <i class="fab fa-r-project text-blue-800 mr-2"></i>R: Advanced Analysis
                    </h3>
                    <div class="code-block r-code">
                        <pre><code>library(deSolve)
library(ggplot2)
library(dplyr)
library(phaseR)
library(rootSolve)

# Advanced ODE Analysis Tools in R
AdvancedAnalysis <- list(

  # Phase line analysis function
  phase_line_analysis = function(f, y_range, n_points = 1000) {
    y_vals <- seq(y_range[1], y_range[2], length.out = n_points)
    dydt_vals <- f(y_vals)

    # Find equilibria (sign changes)
    sign_changes <- which(diff(sign(dydt_vals)) != 0)
    equilibria <- c()

    for (i in sign_changes) {
      try({
        eq <- uniroot(f, c(y_vals[i], y_vals[i+1]))$root
        equilibria <- c(equilibria, eq)
      }, silent = TRUE)
    }

    # Create phase line data
    phase_data <- data.frame(
      y = y_vals,
      dydt = dydt_vals
    )

    return(list(data = phase_data, equilibria = equilibria))
  },

  # Bifurcation analysis
  bifurcation_analysis = function(f, param_range, n_params = 100) {
    params <- seq(param_range[1], param_range[2], length.out = n_params)
    bifurcation_data <- data.frame()

    for (p in params) {
      # Find equilibria for this parameter
      f_param <- function(y) f(y, p)

      # Search for equilibria in reasonable range
      y_search <- seq(-10, 10, by = 0.1)
      equilibria <- c()

      for (i in 1:(length(y_search)-1)) {
        if (sign(f_param(y_search[i])) != sign(f_param(y_search[i+1]))) {
          try({
            eq <- uniroot(f_param, c(y_search[i], y_search[i+1]))$root
            equilibria <- c(equilibria, eq)
          }, silent = TRUE)
        }
      }

      # Check stability of equilibria
      for (eq in equilibria) {
        # Numerical derivative for stability
        h <- 1e-6
        fprime <- (f_param(eq + h) - f_param(eq - h)) / (2 * h)
        stable <- fprime < 0

        bifurcation_data <- rbind(bifurcation_data,
                                data.frame(parameter = p,
                                         equilibrium = eq,
                                         stable = stable))
      }
    }

    return(bifurcation_data)
  }
)

# Example: Harvesting model analysis
harvesting_model <- function(P, H = 2) {
  r <- 1
  K <- 10
  return(r * P * (1 - P/K) - H)
}

# Phase line analysis for harvesting model
phase_result <- AdvancedAnalysis$phase_line_analysis(harvesting_model, c(-1, 12))

# Visualize phase line
phase_plot <- ggplot(phase_result$data, aes(x = y, y = dydt)) +
  geom_line(size = 1.2, color = "blue") +
  geom_hline(yintercept = 0, linetype = "dashed", alpha = 0.7) +
  geom_vline(xintercept = 0, linetype = "dashed", alpha = 0.7) +
  labs(
    title = "Phase Line Analysis: Harvesting Model",
    x = "Population (P)",
    y = "dP/dt"
  ) +
  theme_minimal()

print(phase_plot)</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <!-- Case Studies -->
        <section id="case-studies" class="mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">
                <i class="fas fa-project-diagram text-indigo-600 mr-3"></i>Case Studies
            </h2>

            <div class="grid md:grid-cols-2 gap-6">
                <div class="definition-box">
                    <h3 class="text-xl font-bold mb-3"><i class="fas fa-virus mr-2"></i>Epidemic Modeling</h3>
                    <p class="mb-3">The SIR model uses a system of ODEs to model disease spread:</p>
                    <div class="text-sm space-y-2">
                        <p>$\frac{dS}{dt} = -\beta SI/N$ (Susceptible)</p>
                        <p>$\frac{dI}{dt} = \beta SI/N - \gamma I$ (Infected)</p>
                        <p>$\frac{dR}{dt} = \gamma I$ (Recovered)</p>
                    </div>
                    <p class="text-sm mt-3">For early stages, infected population follows: $\frac{dI}{dt} \approx (\beta - \gamma)I$</p>
                </div>

                <div class="application-box">
                    <h3 class="text-xl font-bold mb-3"><i class="fas fa-industry mr-2"></i>Chemical Reactor</h3>
                    <p class="mb-3">A continuous stirred tank reactor (CSTR) follows:</p>
                    <div class="text-sm space-y-2">
                        <p>$\frac{dC}{dt} = \frac{F}{V}(C_{in} - C) - kC^n$</p>
                        <p>Where: F = flow rate, V = volume, k = reaction rate constant</p>
                    </div>
                    <p class="text-sm mt-3">For first-order reactions (n=1), this becomes linear first-order ODE.</p>
                </div>
            </div>
        </section>

        <!-- Summary -->
        <section class="mb-12">
            <div class="bg-gradient-to-r from-blue-600 to-purple-700 text-white p-8 rounded-lg">
                <h2 class="text-2xl font-bold mb-4"><i class="fas fa-graduation-cap mr-3"></i>Chapter Summary</h2>
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="text-lg font-bold mb-3">Key Modeling Steps</h3>
                        <ul class="space-y-2 text-sm">
                            <li>1. <strong>Identify variables:</strong> What changes over time?</li>
                            <li>2. <strong>Find relationships:</strong> How do variables relate?</li>
                            <li>3. <strong>Formulate ODE:</strong> Express rate of change</li>
                            <li>4. <strong>Solve equation:</strong> Use appropriate method</li>
                            <li>5. <strong>Validate model:</strong> Check against data</li>
                            <li>6. <strong>Interpret results:</strong> What do solutions mean?</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold mb-3">Common Applications</h3>
                        <ul class="space-y-2 text-sm">
                            <li>• <strong>Population dynamics:</strong> Growth and harvesting</li>
                            <li>• <strong>Economics:</strong> Interest, loans, investments</li>
                            <li>• <strong>Physics:</strong> Cooling, radioactive decay</li>
                            <li>• <strong>Engineering:</strong> RC circuits, control systems</li>
                            <li>• <strong>Biology:</strong> Drug concentration, enzyme kinetics</li>
                            <li>• <strong>Chemistry:</strong> Reaction rates, mixing problems</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <script>
        // Classification Chart - Decision Tree for ODE Types
        const classificationCtx = document.getElementById('classificationChart').getContext('2d');
        const classificationChart = new Chart(classificationCtx, {
            type: 'doughnut',
            data: {
                labels: ['Separable', 'Linear First-Order', 'Exact', 'Other Methods'],
                datasets: [{
                    data: [35, 40, 15, 10],
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.8)',
                        'rgba(54, 162, 235, 0.8)',
                        'rgba(255, 206, 86, 0.8)',
                        'rgba(75, 192, 192, 0.8)'
                    ],
                    borderColor: [
                        'rgba(255, 99, 132, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(75, 192, 192, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'ODE Classification Decision Tree',
                        font: { size: 16 }
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Mixing Chart - Salt Water Tank Problem
        const mixingCtx = document.getElementById('mixingChart').getContext('2d');
        
        // Generate mixing problem data
        const timePoints = Array.from({length: 501}, (_, i) => i);
        const concentration = timePoints.map(t => 0.5 * (1 - Math.exp(-0.01 * t)));
        const equilibrium = timePoints.map(t => 0.5);
        
        mixingChartInstance = new Chart(mixingCtx, {
            type: 'line',
            data: {
                labels: timePoints,
                datasets: [{
                    label: 'Concentration C(t)',
                    data: concentration,
                    borderColor: 'rgb(54, 162, 235)',
                    backgroundColor: 'rgba(54, 162, 235, 0.1)',
                    fill: true,
                    tension: 0.4,
                    borderWidth: 3
                }, {
                    label: 'Equilibrium (0.5 kg/L)',
                    data: equilibrium,
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.1)',
                    borderDash: [5, 5],
                    fill: false,
                    tension: 0,
                    borderWidth: 2
                }, {
                    label: '95% of Equilibrium',
                    data: timePoints.map(t => 0.475),
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    borderDash: [10, 5],
                    fill: false,
                    tension: 0,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Time (minutes)',
                            font: { size: 14 }
                        },
                        grid: {
                            alpha: 0.3
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Concentration (kg/L)',
                            font: { size: 14 }
                        },
                        min: 0,
                        max: 0.6,
                        grid: {
                            alpha: 0.3
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Salt Water Tank Mixing Problem',
                        font: { size: 16 }
                    },
                    legend: {
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            afterLabel: function(context) {
                                if (context.datasetIndex === 0) {
                                    const t = context.parsed.x;
                                    const percentage = ((context.parsed.y / 0.5) * 100).toFixed(1);
                                    return `${percentage}% of equilibrium at t=${t} min`;
                                }
                                return null;
                            }
                        }
                    }
                },
                elements: {
                    point: {
                        radius: 0,
                        hoverRadius: 5
                    }
                }
            }
        });

        // Population Chart - Improved version with multiple scenarios
        const populationCtx = document.getElementById('populationChart').getContext('2d');
        
        // Generate population data
        const timeRange = Array.from({length: 51}, (_, i) => i);
        const P0 = 50, r = 0.1, K = 1000;
        
        // Exponential growth
        const exponentialData = timeRange.map(t => P0 * Math.exp(r * t));
        
        // Logistic growth
        const logisticData = timeRange.map(t => K / (1 + (K/P0 - 1) * Math.exp(-r * t)));
        
        // Growth with harvesting (H = 25)
        const harvestingData = timeRange.map(t => {
            // Approximate solution for harvesting model
            const H = 25;
            const equilibrium = K * (1 - Math.sqrt(1 - 4*H/(r*K)));
            if (equilibrium > 0) {
                return equilibrium + (P0 - equilibrium) * Math.exp(-r * (1 - 2*equilibrium/K) * t);
            }
            return 0;
        });
        
        const populationChart = new Chart(populationCtx, {
            type: 'line',
            data: {
                labels: timeRange,
                datasets: [{
                    label: 'Exponential Growth',
                    data: exponentialData,
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.1)',
                    fill: false,
                    tension: 0.4,
                    borderWidth: 3
                }, {
                    label: 'Logistic Growth',
                    data: logisticData,
                    borderColor: 'rgb(54, 162, 235)',
                    backgroundColor: 'rgba(54, 162, 235, 0.1)',
                    fill: false,
                    tension: 0.4,
                    borderWidth: 3
                }, {
                    label: 'With Harvesting (H=25)',
                    data: harvestingData,
                    borderColor: 'rgb(255, 206, 86)',
                    backgroundColor: 'rgba(255, 206, 86, 0.1)',
                    fill: false,
                    tension: 0.4,
                    borderWidth: 3
                }, {
                    label: 'Carrying Capacity',
                    data: timeRange.map(t => K),
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    borderDash: [10, 5],
                    fill: false,
                    tension: 0,
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Time (years)',
                            font: { size: 14 }
                        },
                        grid: {
                            alpha: 0.3
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Population',
                            font: { size: 14 }
                        },
                        min: 0,
                        max: 1200,
                        grid: {
                            alpha: 0.3
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Population Growth Models Comparison',
                        font: { size: 16 }
                    },
                    legend: {
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            afterLabel: function(context) {
                                if (context.datasetIndex === 1) { // Logistic model
                                    const t = context.parsed.x;
                                    const P = context.parsed.y;
                                    const growthRate = (r * P * (1 - P/K)).toFixed(2);
                                    return `Growth rate: ${growthRate} individuals/year`;
                                }
                                return null;
                            }
                        }
                    }
                },
                elements: {
                    point: {
                        radius: 0,
                        hoverRadius: 5
                    }
                }
            }
        });

        // Economic Models Visualization
        const economicsCtx = document.getElementById('economicsChart').getContext('2d');
        
        // Generate economic data
        const loanAmortization = timeRange.map(t => {
            const r_annual = 0.05;
            const P = 600;
            const D_0 = 100000;
            const k = r_annual / 12;
            return (D_0 - P/k) * Math.exp(-k * t) + P/k;
        });
        
        const savingsWithDeposits = timeRange.map(t => {
            const r_annual = 0.03;
            const D = 500;
            const S_0 = 10000;
            const r = r_annual / 12;
            return S_0 * Math.exp(r * t) + (D * (Math.exp(r * t) - 1) / r);
        });
        
        const compoundInterest = timeRange.map(t => {
            const A_0 = 10000;
            const r_annual = 0.07;
            return A_0 * Math.exp(r_annual * t);
        });
        
        const economicsChart = new Chart(economicsCtx, {
            type: 'line',
            data: {
                labels: timeRange,
                datasets: [{
                    label: 'Loan Amortization',
                    data: loanAmortization,
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.1)',
                    fill: false,
                    tension: 0.4,
                    borderWidth: 3
                }, {
                    label: 'Savings Account',
                    data: savingsWithDeposits,
                    borderColor: 'rgb(54, 162, 235)',
                    backgroundColor: 'rgba(54, 162, 235, 0.1)',
                    fill: false,
                    tension: 0.4,
                    borderWidth: 3
                }, {
                    label: 'Compound Interest',
                    data: compoundInterest,
                    borderColor: 'rgb(255, 206, 86)',
                    backgroundColor: 'rgba(255, 206, 86, 0.1)',
                    fill: false,
                    tension: 0.4,
                    borderWidth: 3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Time (years)',
                            font: { size: 14 }
                        },
                        grid: {
                            alpha: 0.3
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Amount ($)',
                            font: { size: 14 }
                        },
                        min: 0,
                        grid: {
                            alpha: 0.3
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Economic Models Visualization',
                        font: { size: 16 }
                    },
                    legend: {
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (context.parsed.y !== null) {
                                    label += `: $${context.parsed.y.toFixed(2)}`;
                                }
                                return label;
                            }
                        }
                    }
                },
                elements: {
                    point: {
                        radius: 0,
                        hoverRadius: 5
                    }
                }
            }
        });

        // Economics Chart - Financial Models
        const economicsCanvas = document.getElementById('economicsChart');
        if (economicsCanvas) {
            const economicsChart = new Chart(economicsCanvas.getContext('2d'), {
                type: 'line',
                data: {
                    labels: Array.from({length: 121}, (_, i) => i), // 0 to 120 months
                    datasets: [{
                        label: 'Loan Balance ($)',
                        data: Array.from({length: 121}, (_, i) => {
                            // Loan amortization: D(t) = (D0 - P/r)e^(rt) + P/r
                            const D0 = 100000, r = 0.05/12, P = 600; // Monthly
                            const t = i;
                            if (t === 0) return D0;
                            const balance = (D0 - P/r) * Math.exp(r * t) + P/r;
                            return Math.max(0, balance); // Can't go negative
                        }),
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.1)',
                        fill: false,
                        tension: 0.4,
                        borderWidth: 3,
                        yAxisID: 'y'
                    }, {
                        label: 'Savings Balance ($)',
                        data: Array.from({length: 121}, (_, i) => {
                            // Savings with deposits: S(t) = (S0 + D/r)e^(rt) - D/r
                            const S0 = 10000, r = 0.03/12, D = 500; // Monthly
                            const t = i;
                            return (S0 + D/r) * Math.exp(r * t) - D/r;
                        }),
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.1)',
                        fill: false,
                        tension: 0.4,
                        borderWidth: 3,
                        yAxisID: 'y'
                    }, {
                        label: 'Compound Interest ($)',
                        data: Array.from({length: 121}, (_, i) => {
                            // Compound interest: A(t) = A0 * e^(rt)
                            const A0 = 10000, r = 0.07/12; // Monthly compounding
                            const t = i;
                            return A0 * Math.exp(r * t);
                        }),
                        borderColor: 'rgb(255, 206, 86)',
                        backgroundColor: 'rgba(255, 206, 86, 0.1)',
                        fill: false,
                        tension: 0.4,
                        borderWidth: 3,
                        yAxisID: 'y'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Time (months)',
                                font: { size: 14 }
                            },
                            grid: {
                                alpha: 0.3
                            }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'Amount ($)',
                                font: { size: 14 }
                            },
                            grid: {
                                alpha: 0.3
                            },
                            ticks: {
                                callback: function(value) {
                                    return '$' + value.toLocaleString();
                                }
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Economic Models: Loans, Savings, and Investments',
                            font: { size: 16 }
                        },
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const value = context.parsed.y;
                                    return context.dataset.label + ': $' + value.toLocaleString();
                                },
                                afterLabel: function(context) {
                                    if (context.datasetIndex === 0) { // Loan
                                        const months = context.parsed.x;
                                        const years = (months / 12).toFixed(1);
                                        return `After ${years} years`;
                                    }
                                    return null;
                                }
                            }
                        }
                    },
                    elements: {
                        point: {
                            radius: 0,
                            hoverRadius: 5
                        }
                    }
                }
            });
        }

        // Cooling Chart - Newton's Law of Cooling
        const coolingCanvas = document.getElementById('coolingChart');
        if (coolingCanvas) {
            const timePointsCooling = Array.from({length: 61}, (_, i) => i); // 0 to 60 minutes
            
            const coolingChart = new Chart(coolingCanvas.getContext('2d'), {
                type: 'line',
                data: {
                    labels: timePointsCooling,
                    datasets: [{
                        label: 'Hot Coffee (90°C → 20°C)',
                        data: timePointsCooling.map(t => 20 + (90 - 20) * Math.exp(-0.067 * t)),
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.1)',
                        fill: false,
                        tension: 0.4,
                        borderWidth: 3
                    }, {
                        label: 'Cold Drink (5°C → 20°C)',
                        data: timePointsCooling.map(t => 20 + (5 - 20) * Math.exp(-0.050 * t)),
                        borderColor: 'rgb(54, 162, 235)',
                        backgroundColor: 'rgba(54, 162, 235, 0.1)',
                        fill: false,
                        tension: 0.4,
                        borderWidth: 3
                    }, {
                        label: 'Lukewarm Water (35°C → 20°C)',
                        data: timePointsCooling.map(t => 20 + (35 - 20) * Math.exp(-0.040 * t)),
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.1)',
                        fill: false,
                        tension: 0.4,
                        borderWidth: 3
                    }, {
                        label: 'Room Temperature',
                        data: timePointsCooling.map(t => 20),
                        borderColor: 'rgb(255, 206, 86)',
                        backgroundColor: 'rgba(255, 206, 86, 0.1)',
                        borderDash: [10, 5],
                        fill: false,
                        tension: 0,
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Time (minutes)',
                                font: { size: 14 }
                            },
                            grid: {
                                alpha: 0.3
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Temperature (°C)',
                                font: { size: 14 }
                            },
                            grid: {
                                alpha: 0.3
                            },
                            min: 0,
                            max: 100
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Newton\'s Law of Cooling - Temperature vs Time',
                            font: { size: 16 }
                        },
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const temp = context.parsed.y.toFixed(1);
                                    return context.dataset.label + ': ' + temp + '°C';
                                },
                                afterLabel: function(context) {
                                    if (context.datasetIndex < 3) { // Not room temperature line
                                        const t = context.parsed.x;
                                        const temp = context.parsed.y;
                                        const roomTemp = 20;
                                        const percentageToEquilibrium = (Math.abs(temp - roomTemp) / Math.abs(context.dataset.data[0] - roomTemp) * 100).toFixed(1);
                                        return `${100 - percentageToEquilibrium}% equilibrated at t=${t} min`;
                                    }
                                    return null;
                                }
                            }
                        }
                    },
                    elements: {
                        point: {
                            radius: 0,
                            hoverRadius: 5
                        }
                    }
                }
            });
        }

        // Enhanced visualization creation functions
        function createEconomicCharts() {
            // Economic Price Dynamics Chart
            const priceCtx = document.getElementById('economicPriceChart');
            if (priceCtx) {
                const timeRange = Array.from({length: 101}, (_, i) => i / 10);
                const equilibriumPrice = 100;
                const initialPrices = [80, 120, 90, 110];
                const adjustmentRate = 0.3;
                
                const datasets = initialPrices.map((P0, index) => {
                    const prices = timeRange.map(t => 
                        equilibriumPrice + (P0 - equilibriumPrice) * Math.exp(-adjustmentRate * t)
                    );
                    return {
                        label: `Initial Price: $${P0}`,
                        data: prices,
                        borderColor: `hsl(${index * 60}, 70%, 50%)`,
                        backgroundColor: `hsl(${index * 60}, 70%, 50%, 0.1)`,
                        fill: false,
                        tension: 0.4
                    };
                });

                datasets.push({
                    label: 'Equilibrium Price',
                    data: timeRange.map(() => equilibriumPrice),
                    borderColor: '#333',
                    borderDash: [5, 5],
                    pointRadius: 0
                });

                new Chart(priceCtx, {
                    type: 'line',
                    data: {
                        labels: timeRange,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Market Price Adjustment Dynamics'
                            },
                            legend: { position: 'top' }
                        },
                        scales: {
                            x: { title: { display: true, text: 'Time (months)' } },
                            y: { title: { display: true, text: 'Price ($)' } }
                        }
                    }
                });
            }

            // Economic Growth Chart
            const growthCtx = document.getElementById('economicGrowthChart');
            if (growthCtx) {
                const timeRange = Array.from({length: 51}, (_, i) => i);
                const scenarios = [
                    { label: 'Low Growth (2%)', rate: 0.02, color: '#ff6b6b' },
                    { label: 'Moderate Growth (5%)', rate: 0.05, color: '#4ecdc4' },
                    { label: 'High Growth (8%)', rate: 0.08, color: '#45b7d1' }
                ];
                const initialValue = 1000;

                const datasets = scenarios.map(scenario => ({
                    label: scenario.label,
                    data: timeRange.map(t => initialValue * Math.exp(scenario.rate * t)),
                    borderColor: scenario.color,
                    backgroundColor: scenario.color + '20',
                    fill: false,
                    tension: 0.4
                }));

                new Chart(growthCtx, {
                    type: 'line',
                    data: {
                        labels: timeRange,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Economic Growth Scenarios'
                            }
                        },
                        scales: {
                            x: { title: { display: true, text: 'Time (years)' } },
                            y: { 
                                title: { display: true, text: 'Value ($)' },
                                type: 'logarithmic'
                            }
                        }
                    }
                });
            }

            // Investment Chart
            const investmentCtx = document.getElementById('economicInvestmentChart');
            if (investmentCtx) {
                const timeRange = Array.from({length: 101}, (_, i) => i / 2);
                const interestRate = 0.05;
                const initialAmount = 10000;
                const scenarios = [
                    { deposit: 0, label: 'No Deposits' },
                    { deposit: 500, label: 'Monthly $500 Deposits' },
                    { deposit: 1000, label: 'Monthly $1000 Deposits' },
                    { deposit: -300, label: 'Monthly $300 Withdrawals' }
                ];

                const datasets = scenarios.map((scenario, index) => {
                    const D = scenario.deposit * 12; // Annual equivalent
                    const data = timeRange.map(t => {
                        if (D === 0) {
                            return initialAmount * Math.exp(interestRate * t);
                        } else {
                            return D/interestRate + (initialAmount - D/interestRate) * Math.exp(interestRate * t);
                        }
                    });
                    
                    return {
                        label: scenario.label,
                        data: data,
                        borderColor: `hsl(${index * 90}, 70%, 50%)`,
                        backgroundColor: `hsl(${index * 90}, 70%, 50%, 0.1)`,
                        fill: false,
                        tension: 0.4
                    };
                });

                new Chart(investmentCtx, {
                    type: 'line',
                    data: {
                        labels: timeRange,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Investment Growth with Continuous Deposits/Withdrawals'
                            }
                        },
                        scales: {
                            x: { title: { display: true, text: 'Time (years)' } },
                            y: { title: { display: true, text: 'Account Balance ($)' } }
                        }
                    }
                });
            }
        }

        function createPhysicsCharts() {
            // Radioactive Decay Chart
            const decayCtx = document.getElementById('physicsDecayChart');
            if (decayCtx) {
                const timeRange = Array.from({length: 101}, (_, i) => i);
                const isotopes = [
                    { name: 'Carbon-14', halfLife: 5730, color: '#ff6b6b' },
                    { name: 'Uranium-238', halfLife: 4468000, color: '#4ecdc4' },
                    { name: 'Tritium', halfLife: 12.3, color: '#45b7d1' },
                    { name: 'Iodine-131', halfLife: 8.02, color: '#f7dc6f' }
                ];

                const datasets = isotopes.map(isotope => {
                    const lambda = Math.log(2) / isotope.halfLife;
                    const data = timeRange.map(t => Math.exp(-lambda * t));
                    
                    return {
                        label: `${isotope.name} (t₁/₂ = ${isotope.halfLife} years)`,
                        data: data,
                        borderColor: isotope.color,
                        backgroundColor: isotope.color + '20',
                        fill: false,
                        tension: 0.4
                    };
                });

                new Chart(decayCtx, {
                    type: 'line',
                    data: {
                        labels: timeRange,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Radioactive Decay of Different Isotopes'
                            }
                        },
                        scales: {
                            x: { title: { display: true, text: 'Time (years)' } },
                            y: { 
                                title: { display: true, text: 'Fraction Remaining' },
                                type: 'logarithmic'
                            }
                        }
                    }
                });
            }

            // Circuit Response Chart
            const circuitCtx = document.getElementById('physicsCircuitChart');
            if (circuitCtx) {
                const timeRange = Array.from({length: 201}, (_, i) => i * 0.01);
                const circuits = [
                    { R: 100, L: 0.1, label: 'Fast Circuit (τ = 1ms)', color: '#ff6b6b' },
                    { R: 1000, L: 1, label: 'Medium Circuit (τ = 1ms)', color: '#4ecdc4' },
                    { R: 10000, L: 10, label: 'Slow Circuit (τ = 1ms)', color: '#45b7d1' }
                ];
                const inputVoltage = 5;

                const datasets = circuits.map(circuit => {
                    const tau = circuit.L / circuit.R;
                    const data = timeRange.map(t => inputVoltage * (1 - Math.exp(-t / tau)));
                    
                    return {
                        label: circuit.label,
                        data: data,
                        borderColor: circuit.color,
                        backgroundColor: circuit.color + '20',
                        fill: false,
                        tension: 0.4
                    };
                });

                datasets.push({
                    label: 'Input Voltage',
                    data: timeRange.map(() => inputVoltage),
                    borderColor: '#333',
                    borderDash: [5, 5],
                    pointRadius: 0
                });

                new Chart(circuitCtx, {
                    type: 'line',
                    data: {
                        labels: timeRange,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'RL Circuit Step Response'
                            }
                        },
                        scales: {
                            x: { title: { display: true, text: 'Time (s)' } },
                            y: { title: { display: true, text: 'Current (A)' } }
                        }
                    }
                });
            }

            // Enhanced Cooling Chart
            const coolingCtx = document.getElementById('physicsCoolingChart');
            if (coolingCtx) {
                const timeRange = Array.from({length: 201}, (_, i) => i);
                const roomTemp = 20;
                const scenarios = [
                    { T0: 100, k: 0.05, material: 'Metal (fast cooling)', color: '#ff6b6b' },
                    { T0: 100, k: 0.02, material: 'Ceramic (medium cooling)', color: '#4ecdc4' },
                    { T0: 100, k: 0.01, material: 'Insulated (slow cooling)', color: '#45b7d1' },
                    { T0: 150, k: 0.03, material: 'Hot metal', color: '#f7dc6f' }
                ];

                const datasets = scenarios.map(scenario => {
                    const data = timeRange.map(t => 
                        roomTemp + (scenario.T0 - roomTemp) * Math.exp(-scenario.k * t)
                    );
                    
                    return {
                        label: scenario.material,
                        data: data,
                        borderColor: scenario.color,
                        backgroundColor: scenario.color + '20',
                        fill: false,
                        tension: 0.4
                    };
                });

                datasets.push({
                    label: 'Room Temperature',
                    data: timeRange.map(() => roomTemp),
                    borderColor: '#333',
                    borderDash: [5, 5],
                    pointRadius: 0
                });

                new Chart(coolingCtx, {
                    type: 'line',
                    data: {
                        labels: timeRange,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Newton\'s Law of Cooling - Multiple Materials'
                            }
                        },
                        scales: {
                            x: { title: { display: true, text: 'Time (minutes)' } },
                            y: { title: { display: true, text: 'Temperature (°C)' } }
                        }
                    }
                });
            }
        }

        function createEngineeringCharts() {
            // Step Response Chart
            const stepCtx = document.getElementById('engineeringStepChart');
            if (stepCtx) {
                const timeRange = Array.from({length: 201}, (_, i) => i * 0.05);
                const systems = [
                    { tau: 0.5, K: 1, label: 'Fast System (τ = 0.5s)', color: '#ff6b6b' },
                    { tau: 1.0, K: 1, label: 'Medium System (τ = 1.0s)', color: '#4ecdc4' },
                    { tau: 2.0, K: 1, label: 'Slow System (τ = 2.0s)', color: '#45b7d1' },
                    { tau: 1.0, K: 2, label: 'High Gain (K = 2)', color: '#f7dc6f' }
                ];

                const datasets = systems.map(system => {
                    const data = timeRange.map(t => system.K * (1 - Math.exp(-t / system.tau)));
                    
                    return {
                        label: system.label,
                        data: data,
                        borderColor: system.color,
                        backgroundColor: system.color + '20',
                        fill: false,
                        tension: 0.4
                    };
                });

                // Add time constant markers
                systems.forEach((system, index) => {
                    if (index < 3) { // Only for different time constants
                        const tauIndex = Math.round(system.tau / 0.05);
                        if (tauIndex < timeRange.length) {
                            datasets.push({
                                label: `τ = ${system.tau}s marker`,
                                data: timeRange.map((t, i) => i === tauIndex ? system.K * 0.632 : null),
                                backgroundColor: system.color,
                                borderColor: system.color,
                                pointRadius: 8,
                                showLine: false,
                                pointStyle: 'circle'
                            });
                        }
                    }
                });

                new Chart(stepCtx, {
                    type: 'line',
                    data: {
                        labels: timeRange,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'First-Order System Step Response'
                            }
                        },
                        scales: {
                            x: { title: { display: true, text: 'Time (s)' } },
                            y: { title: { display: true, text: 'Output' } }
                        }
                    }
                });
            }

            // Frequency Response Chart
            const freqCtx = document.getElementById('engineeringFrequencyChart');
            if (freqCtx) {
                const frequencies = Array.from({length: 100}, (_, i) => Math.pow(10, (i - 50) / 25));
                const systems = [
                    { tau: 0.1, label: 'τ = 0.1s', color: '#ff6b6b' },
                    { tau: 1.0, label: 'τ = 1.0s', color: '#4ecdc4' },
                    { tau: 10.0, label: 'τ = 10.0s', color: '#45b7d1' }
                ];

                const datasets = systems.map(system => {
                    const magnitude = frequencies.map(w => 20 * Math.log10(1 / Math.sqrt(1 + Math.pow(w * system.tau, 2))));
                    
                    return {
                        label: system.label,
                        data: magnitude,
                        borderColor: system.color,
                        backgroundColor: system.color + '20',
                        fill: false,
                        tension: 0.4
                    };
                });

                new Chart(freqCtx, {
                    type: 'line',
                    data: {
                        labels: frequencies.map(f => f.toFixed(3)),
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Frequency Response (Bode Plot - Magnitude)'
                            }
                        },
                        scales: {
                            x: { 
                                title: { display: true, text: 'Frequency (rad/s)' },
                                type: 'logarithmic'
                            },
                            y: { title: { display: true, text: 'Magnitude (dB)' } }
                        }
                    }
                });
            }

            // Control System Chart
            const controlCtx = document.getElementById('engineeringControlChart');
            if (controlCtx) {
                const timeRange = Array.from({length: 301}, (_, i) => i * 0.1);
                const setpoint = 1;
                
                // Simulate step response with different controllers
                const controllers = [
                    { Kp: 1, label: 'P Controller (Kp=1)', color: '#ff6b6b', type: 'proportional' },
                    { Kp: 5, label: 'P Controller (Kp=5)', color: '#4ecdc4', type: 'proportional' },
                    { Kp: 10, label: 'P Controller (Kp=10)', color: '#45b7d1', type: 'proportional' }
                ];

                const datasets = [];
                
                // Add setpoint reference
                datasets.push({
                    label: 'Setpoint',
                    data: timeRange.map(() => setpoint),
                    borderColor: '#333',
                    borderDash: [5, 5],
                    pointRadius: 0,
                    fill: false
                });

                // Add controller responses
                controllers.forEach(controller => {
                    const tau_cl = 1 / (1 + controller.Kp); // Closed-loop time constant
                    const ss_error = 1 / (1 + controller.Kp); // Steady-state error
                    const final_value = setpoint - ss_error;
                    
                    const data = timeRange.map(t => final_value * (1 - Math.exp(-t / tau_cl)));
                    
                    datasets.push({
                        label: controller.label,
                        data: data,
                        borderColor: controller.color,
                        backgroundColor: controller.color + '20',
                        fill: false,
                        tension: 0.4
                    });
                });

                new Chart(controlCtx, {
                    type: 'line',
                    data: {
                        labels: timeRange,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Closed-Loop Control System Response'
                            }
                        },
                        scales: {
                            x: { title: { display: true, text: 'Time (s)' } },
                            y: { title: { display: true, text: 'Output' } }
                        }
                    }
                });
            }
        }

        function createPharmacologyCharts() {
            // IV Bolus Chart
            const ivCtx = document.getElementById('pharmacologyIVChart');
            if (ivCtx) {
                const timeRange = Array.from({length: 121}, (_, i) => i); // 0 to 120 hours
                const dose = 0.25; // mg
                const Vd = 490; // L
                const ke = 0.0103; // h^-1
                const C0 = dose / Vd; // Initial concentration

                const concentration = timeRange.map(t => C0 * Math.exp(-ke * t));

                new Chart(ivCtx.getContext('2d'), {
                    type: 'line',
                    data: {
                        labels: timeRange,
                        datasets: [{
                            label: 'Digoxin Concentration',
                            data: concentration,
                            borderColor: '#10b981',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            fill: true,
                            tension: 0.4,
                            borderWidth: 3
                        }, {
                            label: 'Therapeutic Range (1-2 ng/mL)',
                            data: timeRange.map(() => 1.5),
                            borderColor: '#f59e0b',
                            borderDash: [5, 5],
                            fill: false,
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            x: { title: { display: true, text: 'Time (hours)' } },
                            y: { title: { display: true, text: 'Concentration (ng/mL)' } }
                        },
                        plugins: {
                            title: { display: true, text: 'IV Bolus Pharmacokinetics' }
                        }
                    }
                });
            }

            // Oral Absorption Chart
            const oralCtx = document.getElementById('pharmacologyOralChart');
            if (oralCtx) {
                const timeRange = Array.from({length: 49}, (_, i) => i * 0.5); // 0 to 24 hours
                const ka = 1.5; // absorption rate
                const ke = 0.3; // elimination rate
                const FD_Vd = 2; // F*D/Vd

                const concentration = timeRange.map(t =>
                    (ka * FD_Vd / (ka - ke)) * (Math.exp(-ke * t) - Math.exp(-ka * t))
                );

                new Chart(oralCtx.getContext('2d'), {
                    type: 'line',
                    data: {
                        labels: timeRange,
                        datasets: [{
                            label: 'Oral Absorption Profile',
                            data: concentration,
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            fill: true,
                            tension: 0.4,
                            borderWidth: 3
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            x: { title: { display: true, text: 'Time (hours)' } },
                            y: { title: { display: true, text: 'Concentration (mg/L)' } }
                        },
                        plugins: {
                            title: { display: true, text: 'Oral Absorption Kinetics' }
                        }
                    }
                });
            }

            // Multiple Dose Chart
            const multiCtx = document.getElementById('pharmacologyMultiDoseChart');
            if (multiCtx) {
                const timeRange = Array.from({length: 169}, (_, i) => i); // 0 to 168 hours (1 week)
                const tau = 24; // dosing interval (hours)
                const ke = 0.0289; // elimination rate (h^-1)
                const dose = 0.25;
                const Vd = 490;

                // Multiple dose accumulation
                const concentration = timeRange.map(t => {
                    let C = 0;
                    const nDoses = Math.floor(t / tau) + 1;
                    for (let n = 0; n < nDoses; n++) {
                        const tSinceDose = t - n * tau;
                        if (tSinceDose >= 0) {
                            C += (dose / Vd) * Math.exp(-ke * tSinceDose);
                        }
                    }
                    return C;
                });

                new Chart(multiCtx.getContext('2d'), {
                    type: 'line',
                    data: {
                        labels: timeRange,
                        datasets: [{
                            label: 'Multiple Dose Accumulation',
                            data: concentration,
                            borderColor: '#8b5cf6',
                            backgroundColor: 'rgba(139, 92, 246, 0.1)',
                            fill: true,
                            tension: 0.1,
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            x: { title: { display: true, text: 'Time (hours)' } },
                            y: { title: { display: true, text: 'Concentration (ng/mL)' } }
                        },
                        plugins: {
                            title: { display: true, text: 'Multiple Dosing to Steady State' }
                        }
                    }
                });
            }
        }

        function createAdvancedCharts() {
            // Phase Line Analysis Chart
            const phaseCtx = document.getElementById('advancedPhaseChart');
            if (phaseCtx) {
                const yRange = Array.from({length: 201}, (_, i) => (i - 100) / 10);
                
                // Example: dy/dt = y(y-2)(y+1)
                const dydt = yRange.map(y => y * (y - 2) * (y + 1));
                
                const datasets = [{
                    label: 'dy/dt = y(y-2)(y+1)',
                    data: dydt,
                    borderColor: '#ff6b6b',
                    backgroundColor: '#ff6b6b20',
                    fill: false,
                    tension: 0.4,
                    pointRadius: 0
                }];

                // Add equilibrium points
                const equilibria = [-1, 0, 2];
                equilibria.forEach((eq, index) => {
                    datasets.push({
                        label: `Equilibrium: y = ${eq}`,
                        data: yRange.map((y, i) => Math.abs(y - eq) < 0.1 ? 0 : null),
                        backgroundColor: index % 2 === 0 ? '#4ecdc4' : '#f7dc6f',
                        borderColor: index % 2 === 0 ? '#4ecdc4' : '#f7dc6f',
                        pointRadius: 8,
                        showLine: false,
                        pointStyle: 'circle'
                    });
                });

                new Chart(phaseCtx, {
                    type: 'line',
                    data: {
                        labels: yRange,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Phase Line Analysis: dy/dt vs y'
                            }
                        },
                        scales: {
                            x: { title: { display: true, text: 'y' } },
                            y: { 
                                title: { display: true, text: 'dy/dt' },
                                beginAtZero: true
                            }
                        }
                    }
                });
            }

            // Stability Analysis Chart
            const stabilityCtx = document.getElementById('advancedStabilityChart');
            if (stabilityCtx) {
                const timeRange = Array.from({length: 201}, (_, i) => i * 0.1);
                
                // Different initial conditions for dy/dt = y(y-2)(y+1)
                const initialConditions = [
                    { y0: -2, label: 'y₀ = -2', color: '#ff6b6b' },
                    { y0: -0.5, label: 'y₀ = -0.5', color: '#4ecdc4' },
                    { y0: 0.5, label: 'y₀ = 0.5', color: '#45b7d1' },
                    { y0: 1.5, label: 'y₀ = 1.5', color: '#f7dc6f' },
                    { y0: 3, label: 'y₀ = 3', color: '#bb8fce' }
                ];

                const datasets = initialConditions.map(ic => {
                    // Approximate solution behavior based on equilibria
                    let data;
                    if (ic.y0 < -1) {
                        data = timeRange.map(t => -1 + (ic.y0 + 1) * Math.exp(-t));
                    } else if (ic.y0 > -1 && ic.y0 < 0) {
                        data = timeRange.map(t => -1 + (ic.y0 + 1) * Math.exp(t));
                    } else if (ic.y0 > 0 && ic.y0 < 2) {
                        data = timeRange.map(t => 0 + ic.y0 * Math.exp(-t));
                    } else {
                        data = timeRange.map(t => 2 + (ic.y0 - 2) * Math.exp(t));
                    }
                    
                    return {
                        label: ic.label,
                        data: data,
                        borderColor: ic.color,
                        backgroundColor: ic.color + '20',
                        fill: false,
                        tension: 0.4
                    };
                });

                // Add equilibrium lines
                [-1, 0, 2].forEach((eq, index) => {
                    datasets.push({
                        label: `Equilibrium y = ${eq}`,
                        data: timeRange.map(() => eq),
                        borderColor: '#333',
                        borderDash: [5, 5],
                        pointRadius: 0
                    });
                });

                new Chart(stabilityCtx, {
                    type: 'line',
                    data: {
                        labels: timeRange,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Solution Trajectories and Stability'
                            }
                        },
                        scales: {
                            x: { title: { display: true, text: 'Time' } },
                            y: { title: { display: true, text: 'y(t)' } }
                        }
                    }
                });
            }

            // Direction Field Chart
            const directionCtx = document.getElementById('advancedDirectionChart');
            if (directionCtx) {
                // Create direction field for dy/dt = -2y + 1
                const xRange = Array.from({length: 21}, (_, i) => i * 0.5);
                const yRange = Array.from({length: 21}, (_, i) => (i - 10) * 0.2);
                
                const datasets = [];
                
                // Solution curves
                const initialValues = [-1.5, -1, -0.5, 0, 0.5, 1, 1.5];
                initialValues.forEach((y0, index) => {
                    const solution = xRange.map(t => 0.5 + (y0 - 0.5) * Math.exp(-2 * t));
                    datasets.push({
                        label: `y₀ = ${y0}`,
                        data: solution,
                        borderColor: `hsl(${index * 50}, 70%, 50%)`,
                        backgroundColor: `hsl(${index * 50}, 70%, 50%, 0.1)`,
                        fill: false,
                        tension: 0.4,
                        pointRadius: 0
                    });
                });

                // Equilibrium line
                datasets.push({
                    label: 'Equilibrium: y = 0.5',
                    data: xRange.map(() => 0.5),
                    borderColor: '#333',
                    borderDash: [5, 5],
                    pointRadius: 0
                });

                new Chart(directionCtx, {
                    type: 'line',
                    data: {
                        labels: xRange,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Direction Field and Solution Curves: dy/dt = -2y + 1'
                            }
                        },
                        scales: {
                            x: { title: { display: true, text: 'Time (t)' } },
                            y: { title: { display: true, text: 'y(t)' } }
                        }
                    }
                });
            }

            // Bifurcation Chart
            const bifurcationCtx = document.getElementById('advancedBifurcationChart');
            if (bifurcationCtx) {
                // Harvesting model bifurcation: dP/dt = rP(1-P/K) - H
                const HRange = Array.from({length: 101}, (_, i) => i * 0.3);
                const r = 1, K = 10;
                const Hmax = r * K / 4;
                
                const datasets = [];
                
                // Stable and unstable equilibria
                const stableEq = [], unstableEq = [];
                HRange.forEach(H => {
                    if (H <= Hmax) {
                        const discriminant = r * r * K * K / 4 - 4 * r * H;
                        if (discriminant >= 0) {
                            const eq1 = (r * K + Math.sqrt(discriminant)) / (2 * r);
                            const eq2 = (r * K - Math.sqrt(discriminant)) / (2 * r);
                            stableEq.push(eq1);
                            unstableEq.push(eq2);
                        } else {
                            stableEq.push(null);
                            unstableEq.push(null);
                        }
                    } else {
                        stableEq.push(null);
                        unstableEq.push(null);
                    }
                });

                datasets.push({
                    label: 'Stable Equilibrium',
                    data: stableEq,
                    borderColor: '#4ecdc4',
                    backgroundColor: '#4ecdc4',
                    pointRadius: 2,
                    pointHoverRadius: 4,
                    showLine: true,
                    tension: 0.4
                });

                datasets.push({
                    label: 'Unstable Equilibrium',
                    data: unstableEq,
                    borderColor: '#ff6b6b',
                    backgroundColor: '#ff6b6b',
                    pointRadius: 2,
                    pointHoverRadius: 4,
                    showLine: true,
                    borderDash: [5, 5],
                    tension: 0.4
                });

                // Critical point
                datasets.push({
                    label: `Critical Point (H = ${Hmax.toFixed(2)})`,
                    data: HRange.map((H, i) => Math.abs(H - Hmax) < 0.1 ? K/2 : null),
                    backgroundColor: '#f7dc6f',
                    borderColor: '#f7dc6f',
                    pointRadius: 8,
                    showLine: false,
                    pointStyle: 'star'
                });

                new Chart(bifurcationCtx, {
                    type: 'line',
                    data: {
                        labels: HRange,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Bifurcation Diagram: Harvesting Model'
                            }
                        },
                        scales: {
                            x: { title: { display: true, text: 'Harvest Rate (H)' } },
                            y: { title: { display: true, text: 'Population Equilibrium (P)' } }
                        }
                    }
                });
            }
        }

        // Initialize all new charts when page loads
        function initializeAllCharts() {
            try {
                createEconomicCharts();
                console.log('Economic charts initialized successfully');
            } catch (error) {
                console.error('Error initializing economic charts:', error);
            }

            try {
                createPhysicsCharts();
                console.log('Physics charts initialized successfully');
            } catch (error) {
                console.error('Error initializing physics charts:', error);
            }

            try {
                createPharmacologyCharts();
                console.log('Pharmacology charts initialized successfully');
            } catch (error) {
                console.error('Error initializing pharmacology charts:', error);
            }

            try {
                createEngineeringCharts();
                console.log('Engineering charts initialized successfully');
            } catch (error) {
                console.error('Error initializing engineering charts:', error);
            }

            try {
                createAdvancedCharts();
                console.log('Advanced charts initialized successfully');
            } catch (error) {
                console.error('Error initializing advanced charts:', error);
            }
        }

        // Add interactivity to page elements
        function addInteractivity() {
            // Add hover effects to boxes
            const boxes = document.querySelectorAll('.definition-box, .theorem-box, .example-box, .application-box');
            boxes.forEach(box => {
                box.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                    this.style.transition = 'transform 0.3s ease';
                });
                
                box.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // Add click to expand functionality for code blocks
            const codeBlocks = document.querySelectorAll('.code-block');
            codeBlocks.forEach(block => {
                block.style.cursor = 'pointer';
                block.title = 'Click to toggle expansion';
                
                block.addEventListener('click', function() {
                    if (this.style.maxHeight && this.style.maxHeight !== 'none') {
                        this.style.maxHeight = 'none';
                        this.style.overflow = 'visible';
                    } else {
                        this.style.maxHeight = '300px';
                        this.style.overflow = 'auto';
                    }
                });
                
                // Initial state
                block.style.maxHeight = '300px';
                block.style.overflow = 'auto';
            });
        }

        // Chart update functions for dynamic content
        let mixingChartInstance = null;

        function updateMixingChart() {
            const volume = parseFloat(document.getElementById('volumeSlider').value);
            const flow = parseFloat(document.getElementById('flowSlider').value);
            const concentration = parseFloat(document.getElementById('concentrationSlider').value);

            // Update display values
            document.getElementById('volumeValue').textContent = volume + ' L';
            document.getElementById('flowValue').textContent = flow + ' L/min';
            document.getElementById('concentrationValue').textContent = concentration + ' kg/L';

            // Update current conditions
            document.getElementById('currentVolume').textContent = `Tank volume: ${volume} L`;
            document.getElementById('currentFlow').textContent = `Flow rate: ${flow} L/min`;
            document.getElementById('currentConcentration').textContent = `Inflow concentration: ${concentration} kg/L`;

            // Calculate parameters
            const timeConstant = volume / flow;
            const equilibriumTime = 5 * timeConstant;
            const halfTime = timeConstant * Math.log(2);

            document.getElementById('timeConstant').textContent = `Time constant: τ = ${timeConstant.toFixed(1)} min`;
            document.getElementById('equilibriumTime').textContent = `Equilibrium time: ~${equilibriumTime.toFixed(0)} min`;
            document.getElementById('halfTime').textContent = `Half-time: ~${halfTime.toFixed(1)} min`;

            // Generate new data
            const timePoints = Array.from({length: 501}, (_, i) => i);
            const concentrationData = timePoints.map(t => concentration * (1 - Math.exp(-t / timeConstant)));
            const equilibriumData = timePoints.map(t => concentration);

            // Update chart
            if (mixingChartInstance) {
                mixingChartInstance.data.datasets[0].data = concentrationData;
                mixingChartInstance.data.datasets[1].data = equilibriumData;
                mixingChartInstance.update('none'); // No animation for smooth updates
            }
        }

        function updateMixingChartOld(volumeParam, flowParam, concentrationParam) {
            const V = volumeParam || 1000;
            const r = flowParam || 10;
            const C_in = concentrationParam || 0.5;
            
            const newConcentration = timePoints.map(t => C_in * (1 - Math.exp(-r * t / V)));
            
            mixingChart.data.datasets[0].data = newConcentration;
            mixingChart.data.datasets[1].data = timePoints.map(t => C_in);
            mixingChart.update('active');
        }

        function updatePopulationChart(rParam, KParam, HParam) {
            const newR = rParam || 0.1;
            const newK = KParam || 1000;
            const newH = HParam || 25;
            
            const newLogistic = timeRange.map(t => newK / (1 + (newK/P0 - 1) * Math.exp(-newR * t)));
            const newExponential = timeRange.map(t => P0 * Math.exp(newR * t));
            
            populationChart.data.datasets[0].data = newExponential;
            populationChart.data.datasets[1].data = newLogistic;
            populationChart.data.datasets[3].data = timeRange.map(t => newK);
            populationChart.update('active');
        }

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Navigation highlighting
        function highlightActiveSection() {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('nav a[href^="#"]');
            
            window.addEventListener('scroll', () => {
                let current = '';
                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    const sectionHeight = section.clientHeight;
                    if (pageYOffset >= sectionTop - 200) {
                        current = section.getAttribute('id');
                    }
                });

                navLinks.forEach(link => {
                    link.classList.remove('font-bold', 'text-blue-800');
                    if (link.getAttribute('href') === `#${current}`) {
                        link.classList.add('font-bold', 'text-blue-800');
                    }
                });
            });
        }

        // Loading state management
        function showLoadingState() {
            const chartContainers = document.querySelectorAll('.chart-container');
            chartContainers.forEach(container => {
                if (!container.querySelector('canvas')) return;

                const loadingDiv = document.createElement('div');
                loadingDiv.className = 'loading-overlay';
                loadingDiv.innerHTML = `
                    <div class="flex items-center justify-center h-full">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        <span class="ml-2 text-gray-600">Loading chart...</span>
                    </div>
                `;
                loadingDiv.style.cssText = `
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(255, 255, 255, 0.9);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 10;
                `;

                container.style.position = 'relative';
                container.appendChild(loadingDiv);
            });
        }

        function hideLoadingState() {
            const loadingOverlays = document.querySelectorAll('.loading-overlay');
            loadingOverlays.forEach(overlay => overlay.remove());
        }

        // Initialize everything when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing charts...');
            showLoadingState();

            // Use setTimeout to allow UI to update
            setTimeout(() => {
                // Initialize all charts
                initializeAllCharts();
                hideLoadingState();
                console.log('All charts initialization completed');

                addInteractivity();
                highlightActiveSection();
            }, 100);

            // Mark chart containers as loaded after charts are created
            setTimeout(() => {
                const chartContainers = document.querySelectorAll('.chart-container');
                chartContainers.forEach(container => {
                    container.classList.add('loaded');
                });

                // Add loading animation completion
                document.body.style.opacity = '1';
                document.body.style.transition = 'opacity 0.5s ease-in-out';
            }, 500);

            // Error handling for charts
            window.addEventListener('error', function(e) {
                if (e.message.includes('Chart')) {
                    console.warn('Chart rendering issue detected, attempting recovery...');
                    // Simple recovery mechanism
                    setTimeout(() => {
                        window.location.reload();
                    }, 3000);
                }
            });

            // Smooth scrolling for navigation links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });

        // Expose update functions globally for potential external control
        window.chartUpdaters = {
            updateMixingChart,
            updatePopulationChart
        };

        // Additional chart configuration for better mobile responsiveness
        Chart.defaults.responsive = true;
        Chart.defaults.maintainAspectRatio = false;
        Chart.defaults.devicePixelRatio = window.devicePixelRatio || 1;
        
        // Performance optimization for large datasets
        Chart.defaults.elements.point.radius = 0;
        Chart.defaults.elements.point.hoverRadius = 4;
        Chart.defaults.elements.line.tension = 0.4;
        
        // Accessibility improvements
        Chart.defaults.plugins.legend.labels.usePointStyle = true;
        Chart.defaults.plugins.tooltip.titleFont = { size: 14, weight: 'bold' };
        Chart.defaults.plugins.tooltip.bodyFont = { size: 12 };
        
        // Print-friendly chart settings
        window.addEventListener('beforeprint', function() {
            Chart.helpers.each(Chart.instances, function(chart) {
                chart.resize();
            });
        });
    </script>
</body>
</html>